#!/usr/bin/env python3
"""
Meta-Reflector Node for Dynamic Graph Recompilation
Analyzes agent performance and triggers graph optimizations.
"""

import asyncio
import statistics
from typing import Dict, Any, List, Optional, Tuple
from datetime import datetime, timedelta
from dataclasses import dataclass

from enhanced_agent import AgentState
from performance_tracker import PerformanceTracker
from graph_templates import GraphTemplateManager, GraphTemplate, GraphType

@dataclass
class OptimizationDecision:
    """Represents a decision to optimize the graph."""
    optimization_type: str
    confidence: float
    reasoning: str
    template_recommendation: Optional[str] = None
    new_template_spec: Optional[Dict[str, Any]] = None
    rollback_plan: Optional[str] = None

class MetaReflector:
    """Meta-reflector that analyzes agent performance and optimizes reasoning graphs."""
    
    def __init__(self, performance_tracker: PerformanceTracker, template_manager: GraphTemplateManager):
        self.performance_tracker = performance_tracker
        self.template_manager = template_manager
        self.optimization_history: List[Dict[str, Any]] = []
        self.last_optimization = datetime.now()
        self.optimization_cooldown = timedelta(minutes=30)  # Prevent too frequent optimizations
        
    async def meta_reflect(self, state: AgentState) -> OptimizationDecision:
        """Perform meta-reflection and decide on optimizations."""
        
        # Check if optimization is needed and allowed
        if not self._should_optimize(state):
            return OptimizationDecision(
                optimization_type="none",
                confidence=0.0,
                reasoning="No optimization needed or cooldown active"
            )
        
        # Analyze current performance patterns
        performance_analysis = self._analyze_performance_patterns()
        
        # Detect optimization opportunities
        optimization_opportunities = self._detect_optimization_opportunities(state, performance_analysis)
        
        # Select best optimization
        best_optimization = self._select_best_optimization(optimization_opportunities)
        
        if best_optimization:
            # Log optimization decision
            self.optimization_history.append({
                "timestamp": datetime.now().isoformat(),
                "decision": best_optimization,
                "state_snapshot": self._extract_state_snapshot(state),
                "performance_snapshot": performance_analysis
            })
            
            self.last_optimization = datetime.now()
            
        return best_optimization or OptimizationDecision(
            optimization_type="none",
            confidence=0.0,
            reasoning="No beneficial optimizations identified"
        )
    
    def _should_optimize(self, state: AgentState) -> bool:
        """Determine if optimization should be considered."""
        
        # Check cooldown period
        if datetime.now() - self.last_optimization < self.optimization_cooldown:
            return False
        
        # Check if performance tracking has sufficient data
        if len(self.performance_tracker.query_patterns) < 5:
            return False
        
        # Check for performance issues
        planning_cycles = state.get("planning_cycles", 0)
        if planning_cycles > 5:
            return True
        
        # Check recent success rate
        recent_success_rate = self.performance_tracker._calculate_recent_success_rate()
        if recent_success_rate < 0.7:
            return True
        
        # Check for optimization opportunities
        frequent_patterns = self.performance_tracker._identify_frequent_patterns()
        if len(frequent_patterns) >= 2:
            return True
        
        return False
    
    def _analyze_performance_patterns(self) -> Dict[str, Any]:
        """Analyze current performance patterns."""
        
        # Node performance analysis
        node_analysis = {}
        for node_name, metrics in self.performance_tracker.node_metrics.items():
            node_analysis[node_name] = {
                "avg_time": metrics.average_time,
                "success_rate": metrics.success_rate,
                "usage_count": len(metrics.execution_times),
                "efficiency_score": metrics.success_rate / (metrics.average_time + 0.1)
            }
        
        # Path performance analysis
        path_analysis = {}
        for path_sig, metrics in self.performance_tracker.path_metrics.items():
            path_analysis[path_sig] = {
                "avg_time": metrics.average_time,
                "success_rate": metrics.success_rate,
                "usage_count": metrics.executions,
                "efficiency_score": metrics.success_rate / (metrics.average_time + 0.1)
            }
        
        # Pattern frequency analysis
        pattern_frequency = {}
        for pattern_key, patterns in self.performance_tracker.successful_patterns.items():
            if len(patterns) >= 2:
                avg_confidence = statistics.mean([p.confidence for p in patterns])
                avg_time = statistics.mean([p.execution_time for p in patterns])
                pattern_frequency[pattern_key] = {
                    "frequency": len(patterns),
                    "avg_confidence": avg_confidence,
                    "avg_time": avg_time,
                    "optimization_potential": avg_confidence / (avg_time + 0.1)
                }
        
        return {
            "node_analysis": node_analysis,
            "path_analysis": path_analysis,
            "pattern_frequency": pattern_frequency,
            "total_queries": len(self.performance_tracker.query_patterns),
            "analysis_timestamp": datetime.now().isoformat()
        }
    
    def _detect_optimization_opportunities(self, state: AgentState, analysis: Dict[str, Any]) -> List[OptimizationDecision]:
        """Detect specific optimization opportunities."""
        opportunities = []
        
        # 1. Fast-track opportunity for high-confidence simple queries
        simple_patterns = [
            pattern for pattern, data in analysis["pattern_frequency"].items()
            if "simple" in pattern and data["avg_confidence"] > 0.9 and data["frequency"] >= 3
        ]
        
        if simple_patterns:
            opportunities.append(OptimizationDecision(
                optimization_type="create_fast_track",
                confidence=0.8,
                reasoning=f"Detected {len(simple_patterns)} high-confidence simple patterns that could use fast-track",
                template_recommendation="fast_track"
            ))
        
        # 2. Specialist direct routing opportunity
        specialist_patterns = self._detect_specialist_patterns(analysis)
        if specialist_patterns:
            opportunities.append(OptimizationDecision(
                optimization_type="create_specialist_direct",
                confidence=0.7,
                reasoning=f"Detected clear specialist domain patterns: {', '.join(specialist_patterns)}",
                template_recommendation="specialist_direct"
            ))
        
        # 3. Tool chain optimization
        tool_chains = self._detect_tool_chains(analysis)
        if tool_chains:
            best_chain = max(tool_chains, key=lambda x: x["optimization_potential"])
            opportunities.append(OptimizationDecision(
                optimization_type="create_tool_chain",
                confidence=0.6,
                reasoning=f"Detected frequent tool sequence: {' -> '.join(best_chain['tools'])}",
                new_template_spec={
                    "tool_sequence": best_chain["tools"],
                    "frequency": best_chain["frequency"]
                }
            ))
        
        # 4. Remove inefficient paths
        inefficient_paths = [
            path for path, data in analysis["path_analysis"].items()
            if data["success_rate"] < 0.5 and data["usage_count"] >= 3
        ]
        
        if inefficient_paths:
            opportunities.append(OptimizationDecision(
                optimization_type="remove_inefficient_paths",
                confidence=0.9,
                reasoning=f"Detected consistently inefficient paths: {', '.join(inefficient_paths)}",
                new_template_spec={"paths_to_remove": inefficient_paths}
            ))
        
        # 5. Graph simplification for specific intents
        intent_patterns = self._analyze_intent_patterns(analysis)
        for intent, pattern_data in intent_patterns.items():
            if pattern_data["consistency"] > 0.8 and pattern_data["frequency"] >= 3:
                opportunities.append(OptimizationDecision(
                    optimization_type="create_intent_specific_graph",
                    confidence=pattern_data["consistency"],
                    reasoning=f"Intent '{intent}' shows consistent pattern: {pattern_data['common_path']}",
                    new_template_spec={
                        "intent": intent,
                        "optimized_path": pattern_data["common_path"],
                        "frequency": pattern_data["frequency"]
                    }
                ))
        
        return opportunities
    
    def _detect_specialist_patterns(self, analysis: Dict[str, Any]) -> List[str]:
        """Detect patterns that indicate clear specialist domain queries."""
        specialist_patterns = []
        
        for pattern_key, data in analysis["pattern_frequency"].items():
            if data["frequency"] >= 3 and data["avg_confidence"] > 0.8:
                if any(domain in pattern_key.lower() for domain in ["forensics", "defi", "nft"]):
                    specialist_patterns.append(pattern_key)
        
        return specialist_patterns
    
    def _detect_tool_chains(self, analysis: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Detect frequent tool sequences that could be optimized."""
        tool_chains = []
        
        # Analyze query patterns for common tool sequences
        for pattern in self.performance_tracker.query_patterns[-20:]:  # Recent patterns
            if len(pattern.tools_used) >= 2:
                tool_chains.append({
                    "tools": pattern.tools_used,
                    "execution_time": pattern.execution_time,
                    "confidence": pattern.confidence,
                    "optimization_potential": pattern.confidence / (pattern.execution_time + 0.1)
                })
        
        # Group by tool sequence
        sequence_groups = {}
        for chain in tool_chains:
            sequence_key = "->".join(sorted(chain["tools"]))
            if sequence_key not in sequence_groups:
                sequence_groups[sequence_key] = []
            sequence_groups[sequence_key].append(chain)
        
        # Find frequent sequences
        frequent_chains = []
        for sequence_key, chains in sequence_groups.items():
            if len(chains) >= 3:  # Appears at least 3 times
                avg_potential = statistics.mean([c["optimization_potential"] for c in chains])
                frequent_chains.append({
                    "tools": sequence_key.split("->"),
                    "frequency": len(chains),
                    "optimization_potential": avg_potential
                })
        
        return frequent_chains
    
    def _analyze_intent_patterns(self, analysis: Dict[str, Any]) -> Dict[str, Dict[str, Any]]:
        """Analyze patterns by intent to identify optimization opportunities."""
        intent_patterns = {}
        
        # Group patterns by intent
        for pattern in self.performance_tracker.query_patterns[-50:]:  # Recent patterns
            intent = pattern.intent
            if intent not in intent_patterns:
                intent_patterns[intent] = {
                    "paths": [],
                    "confidences": [],
                    "times": []
                }
            
            intent_patterns[intent]["paths"].append(pattern.successful_path)
            intent_patterns[intent]["confidences"].append(pattern.confidence)
            intent_patterns[intent]["times"].append(pattern.execution_time)
        
        # Analyze consistency for each intent
        result = {}
        for intent, data in intent_patterns.items():
            if len(data["paths"]) >= 3:  # Sufficient data
                # Find most common path
                path_counts = {}
                for path in data["paths"]:
                    path_counts[path] = path_counts.get(path, 0) + 1
                
                most_common_path = max(path_counts, key=path_counts.get)
                consistency = path_counts[most_common_path] / len(data["paths"])
                
                result[intent] = {
                    "common_path": most_common_path,
                    "consistency": consistency,
                    "frequency": len(data["paths"]),
                    "avg_confidence": statistics.mean(data["confidences"]),
                    "avg_time": statistics.mean(data["times"])
                }
        
        return result
    
    def _select_best_optimization(self, opportunities: List[OptimizationDecision]) -> Optional[OptimizationDecision]:
        """Select the best optimization from available opportunities."""
        if not opportunities:
            return None
        
        # Sort by confidence and potential impact
        opportunities.sort(key=lambda x: x.confidence, reverse=True)
        
        # Return the highest confidence optimization
        return opportunities[0]
    
    def _extract_state_snapshot(self, state: AgentState) -> Dict[str, Any]:
        """Extract relevant state information for optimization analysis."""
        return {
            "intent": state.get("intent", "unknown"),
            "complexity_level": state.get("complexity_level", "unknown"),
            "execution_profile": state.get("execution_profile", "balanced"),
            "planning_cycles": state.get("planning_cycles", 0),
            "confidence_level": state.get("confidence_level", 0.0),
            "reasoning_steps": len(state.get("reasoning_history", [])),
            "tools_used": len(state.get("tool_outputs", [])),
            "swarm_used": bool(state.get("swarm_coordination", {}).get("coordination_used", False))
        }
    
    def get_optimization_summary(self) -> Dict[str, Any]:
        """Get summary of optimization history and current status."""
        return {
            "total_optimizations": len(self.optimization_history),
            "last_optimization": self.last_optimization.isoformat(),
            "optimization_types": list(set([opt["decision"].optimization_type for opt in self.optimization_history])),
            "recent_optimizations": self.optimization_history[-5:],  # Last 5
            "next_optimization_available": datetime.now() - self.last_optimization >= self.optimization_cooldown
        }
