#!/usr/bin/env python3
"""
Performance Tracking System for Dynamic Graph Recompilation
Monitors agent performance to enable self-improvement and optimization.
"""

import time
import json
import statistics
from typing import Dict, Any, List, Optional, Callable
from dataclasses import dataclass, field, asdict
from datetime import datetime, timedelta
from collections import defaultdict, deque
from functools import wraps

@dataclass
class NodePerformance:
    """Track performance metrics for individual nodes."""
    node_name: str
    execution_times: List[float] = field(default_factory=list)
    success_count: int = 0
    failure_count: int = 0
    last_execution: Optional[datetime] = None
    average_time: float = 0.0
    success_rate: float = 0.0
    
    def update(self, execution_time: float, success: bool):
        """Update performance metrics."""
        self.execution_times.append(execution_time)
        self.last_execution = datetime.now()
        
        if success:
            self.success_count += 1
        else:
            self.failure_count += 1
            
        # Keep only recent executions (last 100)
        if len(self.execution_times) > 100:
            self.execution_times = self.execution_times[-100:]
            
        # Recalculate metrics
        self.average_time = statistics.mean(self.execution_times)
        total_executions = self.success_count + self.failure_count
        self.success_rate = self.success_count / total_executions if total_executions > 0 else 0.0

@dataclass
class PathPerformance:
    """Track performance of reasoning paths."""
    path_signature: str  # e.g., "router->planner->tools->reflector->synthesis"
    executions: int = 0
    successes: int = 0
    total_time: float = 0.0
    average_time: float = 0.0
    success_rate: float = 0.0
    last_used: Optional[datetime] = None
    
    def update(self, execution_time: float, success: bool):
        """Update path performance."""
        self.executions += 1
        self.total_time += execution_time
        self.last_used = datetime.now()
        
        if success:
            self.successes += 1
            
        self.average_time = self.total_time / self.executions
        self.success_rate = self.successes / self.executions

@dataclass
class QueryPattern:
    """Track patterns in successful queries."""
    pattern_id: str
    intent: str
    complexity: str
    successful_path: str
    tools_used: List[str]
    execution_time: float
    confidence: float
    timestamp: datetime
    
class PerformanceTracker:
    """Advanced performance tracking system for self-improvement."""
    
    def __init__(self):
        self.node_metrics: Dict[str, NodePerformance] = {}
        self.path_metrics: Dict[str, PathPerformance] = {}
        self.query_patterns: List[QueryPattern] = []
        self.optimization_history: List[Dict[str, Any]] = []
        self.current_session_start = datetime.now()
        
        # Pattern detection
        self.successful_patterns = defaultdict(list)
        self.inefficient_patterns = defaultdict(list)
        
        # Optimization triggers
        self.optimization_thresholds = {
            "max_planning_cycles": 5,
            "min_success_rate": 0.7,
            "max_execution_time_multiplier": 2.0,
            "pattern_frequency_threshold": 3
        }
        
    def track_node_execution(self, node_name: str):
        """Decorator to track node execution performance."""
        def decorator(func: Callable):
            @wraps(func)
            async def wrapper(*args, **kwargs):
                start_time = time.time()
                success = True
                error = None
                
                try:
                    result = await func(*args, **kwargs)
                    return result
                except Exception as e:
                    success = False
                    error = str(e)
                    raise
                finally:
                    execution_time = time.time() - start_time
                    self.record_node_execution(node_name, execution_time, success, error)
                    
            return wrapper
        return decorator
    
    def record_node_execution(self, node_name: str, execution_time: float, success: bool, error: Optional[str] = None):
        """Record execution metrics for a node."""
        if node_name not in self.node_metrics:
            self.node_metrics[node_name] = NodePerformance(node_name)
            
        self.node_metrics[node_name].update(execution_time, success)
        
        # Log significant events
        if not success:
            print(f"⚠️  Node {node_name} failed: {error}")
        elif execution_time > 10.0:  # Slow execution
            print(f"🐌 Node {node_name} slow execution: {execution_time:.2f}s")
    
    def record_path_execution(self, path_signature: str, execution_time: float, success: bool):
        """Record execution metrics for a reasoning path."""
        if path_signature not in self.path_metrics:
            self.path_metrics[path_signature] = PathPerformance(path_signature)
            
        self.path_metrics[path_signature].update(execution_time, success)
    
    def record_query_pattern(self, state: Dict[str, Any], success: bool, execution_time: float):
        """Record successful query patterns for optimization."""
        if not success:
            return
            
        # Extract pattern information
        intent = state.get("intent", "unknown")
        complexity = state.get("complexity_level", "unknown")
        reasoning_history = state.get("reasoning_history", [])
        confidence = state.get("confidence_level", 0.0)
        
        # Create path signature from reasoning history
        path_signature = self._extract_path_signature(reasoning_history)
        
        # Extract tools used
        tools_used = self._extract_tools_used(state)
        
        pattern = QueryPattern(
            pattern_id=f"{intent}_{complexity}_{len(self.query_patterns)}",
            intent=intent,
            complexity=complexity,
            successful_path=path_signature,
            tools_used=tools_used,
            execution_time=execution_time,
            confidence=confidence,
            timestamp=datetime.now()
        )
        
        self.query_patterns.append(pattern)
        
        # Track pattern frequency
        pattern_key = f"{intent}_{complexity}_{path_signature}"
        self.successful_patterns[pattern_key].append(pattern)
        
    def _extract_path_signature(self, reasoning_history: List[str]) -> str:
        """Extract reasoning path signature from history."""
        # Simplified path extraction - could be more sophisticated
        key_steps = []
        for step in reasoning_history:
            if "router" in step.lower():
                key_steps.append("router")
            elif "planner" in step.lower():
                key_steps.append("planner")
            elif "tool" in step.lower():
                key_steps.append("tools")
            elif "reflector" in step.lower():
                key_steps.append("reflector")
            elif "synthesis" in step.lower():
                key_steps.append("synthesis")
            elif "swarm" in step.lower():
                key_steps.append("swarm")
                
        return "->".join(key_steps) if key_steps else "unknown"
    
    def _extract_tools_used(self, state: Dict[str, Any]) -> List[str]:
        """Extract tools used from state."""
        tools_used = []
        messages = state.get("messages", [])
        
        for message in messages:
            if hasattr(message, 'tool_calls') and message.tool_calls:
                for tool_call in message.tool_calls:
                    if hasattr(tool_call, 'name'):
                        tools_used.append(tool_call.name)
                    elif isinstance(tool_call, dict) and 'name' in tool_call:
                        tools_used.append(tool_call['name'])
                        
        return list(set(tools_used))  # Remove duplicates
    
    def should_trigger_optimization(self, state: Dict[str, Any]) -> bool:
        """Determine if graph optimization should be triggered."""
        # Check planning cycles
        planning_cycles = state.get("planning_cycles", 0)
        if planning_cycles > self.optimization_thresholds["max_planning_cycles"]:
            return True
            
        # Check recent success rate
        recent_success_rate = self._calculate_recent_success_rate()
        if recent_success_rate < self.optimization_thresholds["min_success_rate"]:
            return True
            
        # Check for frequent successful patterns that could be shortcuts
        frequent_patterns = self._identify_frequent_patterns()
        if frequent_patterns:
            return True
            
        return False
    
    def _calculate_recent_success_rate(self, window_hours: int = 24) -> float:
        """Calculate success rate for recent queries."""
        cutoff_time = datetime.now() - timedelta(hours=window_hours)
        recent_patterns = [p for p in self.query_patterns if p.timestamp > cutoff_time]
        
        if not recent_patterns:
            return 1.0  # No recent data, assume good
            
        # Success rate based on confidence levels
        avg_confidence = statistics.mean([p.confidence for p in recent_patterns])
        return avg_confidence
    
    def _identify_frequent_patterns(self) -> List[str]:
        """Identify patterns that occur frequently and could be optimized."""
        frequent_patterns = []
        threshold = self.optimization_thresholds["pattern_frequency_threshold"]
        
        for pattern_key, patterns in self.successful_patterns.items():
            if len(patterns) >= threshold:
                # Check if pattern is consistently successful
                avg_confidence = statistics.mean([p.confidence for p in patterns])
                if avg_confidence > 0.8:
                    frequent_patterns.append(pattern_key)
                    
        return frequent_patterns
    
    def get_optimization_recommendations(self) -> List[Dict[str, Any]]:
        """Generate optimization recommendations based on performance data."""
        recommendations = []
        
        # Identify slow nodes
        slow_nodes = [
            name for name, metrics in self.node_metrics.items()
            if metrics.average_time > 5.0 and metrics.success_rate > 0.8
        ]
        
        if slow_nodes:
            recommendations.append({
                "type": "optimize_slow_nodes",
                "nodes": slow_nodes,
                "description": f"Optimize slow but successful nodes: {', '.join(slow_nodes)}"
            })
        
        # Identify inefficient paths
        inefficient_paths = [
            path for path, metrics in self.path_metrics.items()
            if metrics.average_time > 10.0 and metrics.success_rate < 0.6
        ]
        
        if inefficient_paths:
            recommendations.append({
                "type": "remove_inefficient_paths",
                "paths": inefficient_paths,
                "description": f"Consider removing inefficient paths: {', '.join(inefficient_paths)}"
            })
        
        # Identify shortcut opportunities
        frequent_patterns = self._identify_frequent_patterns()
        if frequent_patterns:
            recommendations.append({
                "type": "create_shortcuts",
                "patterns": frequent_patterns,
                "description": f"Create shortcuts for frequent patterns: {', '.join(frequent_patterns)}"
            })
        
        return recommendations
    
    def export_metrics(self) -> Dict[str, Any]:
        """Export all performance metrics for analysis."""
        return {
            "node_metrics": {name: asdict(metrics) for name, metrics in self.node_metrics.items()},
            "path_metrics": {path: asdict(metrics) for path, metrics in self.path_metrics.items()},
            "query_patterns": [asdict(pattern) for pattern in self.query_patterns],
            "optimization_history": self.optimization_history,
            "session_start": self.current_session_start.isoformat(),
            "total_queries": len(self.query_patterns)
        }
    
    def reset_session(self):
        """Reset session-specific metrics while keeping historical data."""
        self.current_session_start = datetime.now()
        # Keep historical data but mark new session
        self.optimization_history.append({
            "timestamp": datetime.now().isoformat(),
            "action": "session_reset",
            "metrics_snapshot": self.export_metrics()
        })
