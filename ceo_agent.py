#!/usr/bin/env python3
"""
CEO-Agent: Top-Level Coordinator for Agent Swarms
Orchestrates specialist agents and manages complex multi-domain queries.
"""

import asyncio
from typing import Dict, Any, List, Optional, Literal
from dataclasses import dataclass

from langchain_openai import ChatOpenAI
from langgraph.graph import StateGraph, START, END
from langgraph.types import Command
from langgraph.prebuilt import create_react_agent
from langchain_core.messages import HumanMessage, AIMessage
from langchain_core.tools import tool, InjectedToolCallId
from langgraph.prebuilt import InjectedState

from enhanced_agent import EnhancedLangGraphMCPAgent, AgentState
from specialist_agents import (
    SpecialistAgentFactory, 
    CryptoForensicsAgent, 
    DeFiYieldAgent, 
    NFTMarketAgent
)


class CEOAgent:
    """CEO-Agent that coordinates specialist agents in a swarm architecture."""
    
    def __init__(self, base_agent: EnhancedLangGraphMCPAgent):
        self.base_agent = base_agent
        self.specialists = self._initialize_specialists()
        self.coordination_graph = None
        self._build_coordination_graph()
        
    def _initialize_specialists(self) -> Dict[str, Any]:
        """Initialize all specialist agents."""
        factory = SpecialistAgentFactory()
        
        return {
            "crypto_forensics": factory.create_crypto_forensics_agent(self.base_agent),
            "defi_yield": factory.create_defi_yield_agent(self.base_agent),
            "nft_market": factory.create_nft_market_agent(self.base_agent)
        }
    
    def _create_handoff_tools(self):
        """Create handoff tools for delegating to specialists."""
        
        @tool("delegate_to_crypto_forensics")
        def delegate_to_crypto_forensics(
            query: str,
            context: str = "",
            state: Annotated[AgentState, InjectedState] = None,
            tool_call_id: Annotated[str, InjectedToolCallId] = None
        ) -> Command:
            """Delegate complex crypto forensics investigations to the specialist."""
            return Command(
                goto="crypto_forensics_specialist",
                update={
                    "active_specialist": "crypto_forensics",
                    "specialist_context": {"query": query, "context": context},
                    "messages": state["messages"] + [{
                        "role": "tool",
                        "content": f"Delegating forensics investigation to specialist: {query}",
                        "tool_call_id": tool_call_id
                    }]
                }
            )
        
        @tool("delegate_to_defi_yield")
        def delegate_to_defi_yield(
            query: str,
            context: str = "",
            state: Annotated[AgentState, InjectedState] = None,
            tool_call_id: Annotated[str, InjectedToolCallId] = None
        ) -> Command:
            """Delegate DeFi yield optimization to the specialist."""
            return Command(
                goto="defi_yield_specialist",
                update={
                    "active_specialist": "defi_yield",
                    "specialist_context": {"query": query, "context": context},
                    "messages": state["messages"] + [{
                        "role": "tool",
                        "content": f"Delegating yield optimization to specialist: {query}",
                        "tool_call_id": tool_call_id
                    }]
                }
            )
        
        @tool("delegate_to_nft_market")
        def delegate_to_nft_market(
            query: str,
            context: str = "",
            state: Annotated[AgentState, InjectedState] = None,
            tool_call_id: Annotated[str, InjectedToolCallId] = None
        ) -> Command:
            """Delegate NFT market analysis to the specialist."""
            return Command(
                goto="nft_market_specialist",
                update={
                    "active_specialist": "nft_market",
                    "specialist_context": {"query": query, "context": context},
                    "messages": state["messages"] + [{
                        "role": "tool",
                        "content": f"Delegating NFT analysis to specialist: {query}",
                        "tool_call_id": tool_call_id
                    }]
                }
            )
        
        return [delegate_to_crypto_forensics, delegate_to_defi_yield, delegate_to_nft_market]
    
    def _build_coordination_graph(self):
        """Build the coordination graph for the CEO and specialists."""
        
        # Create CEO supervisor with delegation tools
        handoff_tools = self._create_handoff_tools()
        
        ceo_prompt = """You are the CEO-Agent, a strategic coordinator managing a team of elite crypto specialists:

        🎯 YOUR SPECIALIST TEAM:
        1. **Crypto Forensics Agent**: Expert in transaction tracing, security analysis, and investigations
        2. **DeFi Yield Agent**: Expert in yield optimization, protocol analysis, and risk management  
        3. **NFT Market Agent**: Expert in NFT analysis, market timing, and collection evaluation

        🧠 COORDINATION STRATEGY:
        - Analyze incoming queries to determine which specialist(s) should handle them
        - Delegate complex domain-specific tasks to the appropriate specialist
        - Synthesize results from multiple specialists for comprehensive analysis
        - Handle simple queries directly when specialist expertise isn't needed

        🔄 DELEGATION CRITERIA:
        - **Forensics**: Security concerns, transaction tracing, exploit analysis, suspicious activity
        - **DeFi Yield**: Yield farming, protocol analysis, liquidity provision, APY optimization
        - **NFT Market**: Collection analysis, floor prices, market timing, rarity evaluation

        Use your delegation tools when specialist expertise is needed. Handle simple queries directly.
        """
        
        self.ceo_supervisor = create_react_agent(
            model=self.base_agent.model,
            tools=handoff_tools,
            prompt=ceo_prompt
        )
        
        # Build the coordination graph
        workflow = StateGraph(AgentState)
        
        # Add CEO supervisor
        workflow.add_node("ceo_supervisor", self._ceo_supervisor_node)
        
        # Add specialist nodes
        workflow.add_node("crypto_forensics_specialist", self._crypto_forensics_node)
        workflow.add_node("defi_yield_specialist", self._defi_yield_node)
        workflow.add_node("nft_market_specialist", self._nft_market_node)
        
        # Add synthesis node for multi-specialist coordination
        workflow.add_node("multi_specialist_synthesis", self._multi_specialist_synthesis_node)
        
        # Set entry point
        workflow.set_entry_point("ceo_supervisor")
        
        # Add edges back to CEO for coordination
        workflow.add_edge("crypto_forensics_specialist", "ceo_supervisor")
        workflow.add_edge("defi_yield_specialist", "ceo_supervisor")
        workflow.add_edge("nft_market_specialist", "ceo_supervisor")
        workflow.add_edge("multi_specialist_synthesis", END)
        
        # Compile the coordination graph
        self.coordination_graph = workflow.compile()
    
    async def _ceo_supervisor_node(self, state: AgentState) -> AgentState:
        """CEO supervisor node that coordinates specialists."""
        
        # Check if we're returning from a specialist
        if state.get("active_specialist"):
            # Decide if we need more specialists or can synthesize
            specialist_results = state.get("specialist_context", {})
            
            # Simple logic: if we have results, synthesize
            if specialist_results.get("analysis_complete"):
                return {
                    **state,
                    "next_action": "synthesize"
                }
        
        # Use the CEO supervisor agent for initial routing
        messages = state.get("messages", [])
        if not messages:
            messages = [HumanMessage(content=state.get("input", ""))]
        
        result = await self.ceo_supervisor.ainvoke({"messages": messages})
        
        return {
            **state,
            "messages": result.get("messages", messages),
            "reasoning_history": state.get("reasoning_history", []) + ["CEO coordination decision made"]
        }
    
    async def _crypto_forensics_node(self, state: AgentState) -> AgentState:
        """Execute crypto forensics specialist analysis."""
        specialist = self.specialists["crypto_forensics"]
        context = state.get("specialist_context", {})
        
        query = context.get("query", state.get("input", ""))
        analysis_result = await specialist.analyze(query, context)
        
        return {
            **state,
            "specialist_context": {
                **context,
                "forensics_analysis": analysis_result,
                "analysis_complete": True
            },
            "active_specialist": "",
            "reasoning_history": state.get("reasoning_history", []) + 
                               [f"Crypto forensics analysis completed: {analysis_result.get('confidence_level', 0.0):.2f} confidence"]
        }
    
    async def _defi_yield_node(self, state: AgentState) -> AgentState:
        """Execute DeFi yield specialist analysis."""
        specialist = self.specialists["defi_yield"]
        context = state.get("specialist_context", {})
        
        query = context.get("query", state.get("input", ""))
        analysis_result = await specialist.analyze(query, context)
        
        return {
            **state,
            "specialist_context": {
                **context,
                "yield_analysis": analysis_result,
                "analysis_complete": True
            },
            "active_specialist": "",
            "reasoning_history": state.get("reasoning_history", []) + 
                               [f"DeFi yield analysis completed: {analysis_result.get('optimization_confidence', 0.0):.2f} confidence"]
        }
    
    async def _nft_market_node(self, state: AgentState) -> AgentState:
        """Execute NFT market specialist analysis."""
        specialist = self.specialists["nft_market"]
        context = state.get("specialist_context", {})
        
        query = context.get("query", state.get("input", ""))
        analysis_result = await specialist.analyze(query, context)
        
        return {
            **state,
            "specialist_context": {
                **context,
                "nft_analysis": analysis_result,
                "analysis_complete": True
            },
            "active_specialist": "",
            "reasoning_history": state.get("reasoning_history", []) + 
                               [f"NFT market analysis completed: {analysis_result.get('analysis_confidence', 0.0):.2f} confidence"]
        }
    
    async def _multi_specialist_synthesis_node(self, state: AgentState) -> AgentState:
        """Synthesize results from multiple specialists."""
        context = state.get("specialist_context", {})
        
        # Collect all specialist analyses
        analyses = []
        if "forensics_analysis" in context:
            analyses.append(f"🔍 **Forensics Analysis**: {context['forensics_analysis'].get('findings', '')}")
        if "yield_analysis" in context:
            analyses.append(f"💰 **Yield Strategy**: {context['yield_analysis'].get('strategy', '')}")
        if "nft_analysis" in context:
            analyses.append(f"🎨 **NFT Market**: {context['nft_analysis'].get('market_outlook', '')}")
        
        # Create comprehensive synthesis
        synthesis = f"""# 🚀 **COMPREHENSIVE CRYPTO INTELLIGENCE REPORT**

## 📊 **Multi-Specialist Analysis**
{chr(10).join(analyses)}

## 🎯 **Strategic Recommendations**
Based on the combined expertise of our specialist team, here are the key insights and recommendations for your query.

## ⚡ **Next Steps**
Actionable steps based on the multi-domain analysis.
"""
        
        return {
            **state,
            "final_response": synthesis,
            "next_action": "complete"
        }
    
    async def coordinate(self, query: str, execution_profile: str = "balanced") -> Dict[str, Any]:
        """Main coordination method for the CEO-Agent."""
        
        # Initialize state for coordination
        initial_state = {
            "input": query,
            "intent": "multi_specialist_coordination",
            "final_response": "",
            "plan": ["Analyze query", "Delegate to specialists", "Synthesize results"],
            "current_step": 0,
            "reasoning_history": ["CEO-Agent coordination initiated"],
            "messages": [],
            "tool_outputs": [],
            "information_gathered": {},
            "next_action": "coordinate",
            "confidence_level": 0.0,
            "execution_profile": execution_profile,
            "active_specialist": "",
            "specialist_context": {},
            "swarm_coordination": {"coordination_mode": "active"}
        }
        
        # Execute coordination graph
        result = await self.coordination_graph.ainvoke(initial_state)
        
        return result
