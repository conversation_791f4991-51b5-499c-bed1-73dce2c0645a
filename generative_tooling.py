#!/usr/bin/env python3
"""
Generative Tooling System - Tool Vending Machine for Phase 3
Dynamically creates tools for unknown protocols and smart contracts.
"""

import asyncio
import json
import hashlib
import tempfile
import subprocess
from typing import Dict, Any, List, Optional, Callable
from dataclasses import dataclass, field
from datetime import datetime
import requests
from pathlib import Path

from langchain_openai import ChatOpenAI
from langchain_core.tools import tool
from langchain_core.messages import HumanMessage

@dataclass
class GeneratedTool:
    """Represents a dynamically generated tool."""
    tool_id: str
    name: str
    description: str
    code: str
    abi: Optional[Dict[str, Any]] = None
    contract_address: str = ""
    blockchain: str = ""
    created_at: datetime = field(default_factory=datetime.now)
    usage_count: int = 0
    success_rate: float = 0.0
    last_used: Optional[datetime] = None
    validation_status: str = "pending"  # pending, validated, failed
    risk_level: str = "medium"  # low, medium, high

@dataclass
class ToolGenerationRequest:
    """Request for tool generation."""
    contract_address: str
    blockchain: str
    operation_type: str  # "read", "write", "analyze"
    specific_function: Optional[str] = None
    user_intent: str = ""
    risk_tolerance: str = "medium"

class GenerativeToolingEngine:
    """Tool Vending Machine - Creates tools on demand for any protocol."""
    
    def __init__(self, openai_api_key: Optional[str] = None):
        self.openai_api_key = openai_api_key
        self.code_generator = None
        
        if openai_api_key:
            self.code_generator = ChatOpenAI(
                model="gpt-4o",
                api_key=openai_api_key,
                temperature=0.1
            )
        
        # Tool storage and management
        self.generated_tools: Dict[str, GeneratedTool] = {}
        self.tool_cache: Dict[str, str] = {}  # contract_address -> tool_id
        self.validation_history: List[Dict[str, Any]] = []
        
        # Blockchain explorers for ABI fetching
        self.abi_sources = {
            "ethereum": [
                "https://api.etherscan.io/api",
                "https://api.polygonscan.com/api"
            ],
            "solana": [
                "https://api.solscan.io",
                "https://api.solana.fm"
            ],
            "bsc": [
                "https://api.bscscan.com/api"
            ]
        }
        
        # Code templates for common patterns
        self.code_templates = {
            "erc20_reader": self._get_erc20_reader_template(),
            "defi_analyzer": self._get_defi_analyzer_template(),
            "nft_inspector": self._get_nft_inspector_template(),
            "solana_token": self._get_solana_token_template()
        }
    
    async def generate_tool(self, request: ToolGenerationRequest) -> Optional[GeneratedTool]:
        """Generate a tool for the specified contract and operation."""
        
        print(f"🔧 Generating tool for {request.contract_address} on {request.blockchain}")
        
        # Check if tool already exists
        cache_key = f"{request.contract_address}_{request.operation_type}"
        if cache_key in self.tool_cache:
            tool_id = self.tool_cache[cache_key]
            existing_tool = self.generated_tools.get(tool_id)
            if existing_tool and existing_tool.validation_status == "validated":
                print(f"✅ Using cached tool: {existing_tool.name}")
                return existing_tool
        
        try:
            # Step 1: Fetch contract ABI
            abi = await self._fetch_contract_abi(request.contract_address, request.blockchain)
            if not abi:
                print(f"❌ Could not fetch ABI for {request.contract_address}")
                return None
            
            # Step 2: Analyze ABI and generate code
            generated_code = await self._generate_tool_code(request, abi)
            if not generated_code:
                print(f"❌ Could not generate code for {request.contract_address}")
                return None
            
            # Step 3: Validate generated code
            validation_result = await self._validate_generated_code(generated_code, request)
            
            # Step 4: Create tool object
            tool_id = self._generate_tool_id(request.contract_address, request.operation_type)
            tool = GeneratedTool(
                tool_id=tool_id,
                name=f"{request.blockchain}_{request.operation_type}_{request.contract_address[:8]}",
                description=f"Generated tool for {request.operation_type} operations on {request.contract_address}",
                code=generated_code,
                abi=abi,
                contract_address=request.contract_address,
                blockchain=request.blockchain,
                validation_status=validation_result["status"],
                risk_level=self._assess_risk_level(request, abi)
            )
            
            # Step 5: Store and cache tool
            self.generated_tools[tool_id] = tool
            self.tool_cache[cache_key] = tool_id
            
            print(f"✅ Tool generated successfully: {tool.name}")
            return tool
            
        except Exception as e:
            print(f"❌ Tool generation failed: {e}")
            return None
    
    async def _fetch_contract_abi(self, contract_address: str, blockchain: str) -> Optional[Dict[str, Any]]:
        """Fetch contract ABI from blockchain explorers."""
        
        sources = self.abi_sources.get(blockchain.lower(), [])
        
        for source in sources:
            try:
                if "etherscan" in source or "bscscan" in source or "polygonscan" in source:
                    # Etherscan-style API
                    params = {
                        "module": "contract",
                        "action": "getabi",
                        "address": contract_address,
                        "apikey": "YourApiKeyToken"  # Would need real API key
                    }
                    
                    response = requests.get(source, params=params, timeout=10)
                    data = response.json()
                    
                    if data.get("status") == "1" and data.get("result"):
                        abi_json = json.loads(data["result"])
                        return abi_json
                
                elif "solana" in source:
                    # Solana-specific ABI fetching (simplified)
                    # In reality, would use Anchor IDL or program analysis
                    return {"type": "solana_program", "address": contract_address}
                    
            except Exception as e:
                print(f"⚠️  Failed to fetch ABI from {source}: {e}")
                continue
        
        # Fallback: Generate minimal ABI based on common patterns
        return self._generate_fallback_abi(contract_address, blockchain)
    
    def _generate_fallback_abi(self, contract_address: str, blockchain: str) -> Dict[str, Any]:
        """Generate a minimal ABI for common contract patterns."""
        
        if blockchain.lower() == "ethereum":
            # Common ERC-20 functions
            return [
                {"name": "balanceOf", "type": "function", "inputs": [{"name": "account", "type": "address"}]},
                {"name": "totalSupply", "type": "function", "inputs": []},
                {"name": "name", "type": "function", "inputs": []},
                {"name": "symbol", "type": "function", "inputs": []}
            ]
        elif blockchain.lower() == "solana":
            return {"type": "solana_token", "standard": "spl_token"}
        
        return {}
    
    async def _generate_tool_code(self, request: ToolGenerationRequest, abi: Dict[str, Any]) -> Optional[str]:
        """Generate Python code for interacting with the contract."""
        
        if not self.code_generator:
            # Fallback to template-based generation
            return self._generate_from_template(request, abi)
        
        try:
            # Use LLM to generate sophisticated code
            prompt = self._create_code_generation_prompt(request, abi)
            
            message = HumanMessage(content=prompt)
            response = await self.code_generator.ainvoke([message])
            
            # Extract code from response
            code = self._extract_code_from_response(response.content)
            return code
            
        except Exception as e:
            print(f"⚠️  LLM code generation failed, using template: {e}")
            return self._generate_from_template(request, abi)
    
    def _create_code_generation_prompt(self, request: ToolGenerationRequest, abi: Dict[str, Any]) -> str:
        """Create prompt for LLM code generation."""
        
        return f"""
        Generate Python code for a tool that interacts with a {request.blockchain} smart contract.
        
        CONTRACT DETAILS:
        - Address: {request.contract_address}
        - Blockchain: {request.blockchain}
        - Operation: {request.operation_type}
        - User Intent: {request.user_intent}
        
        CONTRACT ABI:
        {json.dumps(abi, indent=2)}
        
        REQUIREMENTS:
        1. Create a Python function that can {request.operation_type} data from this contract
        2. Include proper error handling and validation
        3. Return structured data in JSON format
        4. Add safety checks for {request.risk_tolerance} risk tolerance
        5. Include docstring with usage examples
        
        SAFETY REQUIREMENTS:
        - Read-only operations for analysis
        - No private key handling
        - Proper input validation
        - Clear error messages
        
        Return only the Python code, properly formatted and ready to execute.
        """
    
    def _extract_code_from_response(self, response_text: str) -> str:
        """Extract Python code from LLM response."""
        
        # Look for code blocks
        if "```python" in response_text:
            start = response_text.find("```python") + 9
            end = response_text.find("```", start)
            return response_text[start:end].strip()
        elif "```" in response_text:
            start = response_text.find("```") + 3
            end = response_text.find("```", start)
            return response_text[start:end].strip()
        
        # If no code blocks, return the whole response
        return response_text.strip()
    
    def _generate_from_template(self, request: ToolGenerationRequest, abi: Dict[str, Any]) -> str:
        """Generate code using templates for common patterns."""
        
        if request.blockchain.lower() == "ethereum" and "balanceOf" in str(abi):
            template = self.code_templates["erc20_reader"]
            return template.format(
                contract_address=request.contract_address,
                blockchain=request.blockchain
            )
        elif request.blockchain.lower() == "solana":
            template = self.code_templates["solana_token"]
            return template.format(
                contract_address=request.contract_address
            )
        
        # Generic template
        return f"""
async def generated_tool_{request.contract_address[:8]}(query: str) -> dict:
    '''Generated tool for {request.operation_type} operations on {request.contract_address}'''
    
    try:
        # Placeholder implementation
        return {{
            "contract_address": "{request.contract_address}",
            "blockchain": "{request.blockchain}",
            "operation": "{request.operation_type}",
            "result": "Tool generated but requires manual implementation",
            "status": "placeholder"
        }}
    except Exception as e:
        return {{"error": str(e), "status": "failed"}}
"""
    
    async def _validate_generated_code(self, code: str, request: ToolGenerationRequest) -> Dict[str, Any]:
        """Validate generated code for safety and functionality."""
        
        validation_result = {
            "status": "pending",
            "issues": [],
            "safety_score": 0.0,
            "functionality_score": 0.0
        }
        
        try:
            # Syntax validation
            compile(code, '<generated_tool>', 'exec')
            validation_result["functionality_score"] += 0.3
            
            # Safety checks
            dangerous_patterns = [
                "exec(", "eval(", "__import__", "open(", "subprocess",
                "private_key", "mnemonic", "seed_phrase"
            ]
            
            safety_issues = [pattern for pattern in dangerous_patterns if pattern in code]
            if not safety_issues:
                validation_result["safety_score"] = 0.9
            else:
                validation_result["issues"].extend(safety_issues)
                validation_result["safety_score"] = 0.3
            
            # Determine overall status
            if validation_result["safety_score"] > 0.7 and validation_result["functionality_score"] > 0.2:
                validation_result["status"] = "validated"
            else:
                validation_result["status"] = "failed"
                
        except SyntaxError as e:
            validation_result["issues"].append(f"Syntax error: {e}")
            validation_result["status"] = "failed"
        
        return validation_result
    
    def _assess_risk_level(self, request: ToolGenerationRequest, abi: Dict[str, Any]) -> str:
        """Assess risk level of the generated tool."""
        
        # Read-only operations are generally low risk
        if request.operation_type == "read":
            return "low"
        
        # Check for high-risk functions in ABI
        high_risk_functions = ["transfer", "approve", "mint", "burn", "withdraw"]
        if any(func in str(abi).lower() for func in high_risk_functions):
            return "high"
        
        return "medium"
    
    def _generate_tool_id(self, contract_address: str, operation_type: str) -> str:
        """Generate unique tool ID."""
        content = f"{contract_address}_{operation_type}_{datetime.now().isoformat()}"
        return hashlib.md5(content.encode()).hexdigest()[:16]
    
    def _get_erc20_reader_template(self) -> str:
        """Template for ERC-20 token reading tools."""
        return """
async def erc20_reader_{contract_address}(query: str) -> dict:
    '''Read ERC-20 token data from {contract_address} on {blockchain}'''
    
    try:
        # Placeholder for ERC-20 reading logic
        return {{
            "contract_address": "{contract_address}",
            "token_name": "Token Name",
            "token_symbol": "SYMBOL",
            "total_supply": "1000000",
            "decimals": 18,
            "status": "success"
        }}
    except Exception as e:
        return {{"error": str(e), "status": "failed"}}
"""
    
    def _get_defi_analyzer_template(self) -> str:
        """Template for DeFi protocol analysis tools."""
        return """
async def defi_analyzer_{contract_address}(query: str) -> dict:
    '''Analyze DeFi protocol at {contract_address}'''
    
    try:
        return {{
            "protocol_type": "DeFi",
            "tvl": "Unknown",
            "apy": "Unknown",
            "risk_score": "Medium",
            "status": "analysis_complete"
        }}
    except Exception as e:
        return {{"error": str(e), "status": "failed"}}
"""
    
    def _get_nft_inspector_template(self) -> str:
        """Template for NFT inspection tools."""
        return """
async def nft_inspector_{contract_address}(query: str) -> dict:
    '''Inspect NFT collection at {contract_address}'''
    
    try:
        return {{
            "collection_name": "NFT Collection",
            "total_supply": "10000",
            "floor_price": "Unknown",
            "status": "inspection_complete"
        }}
    except Exception as e:
        return {{"error": str(e), "status": "failed"}}
"""
    
    def _get_solana_token_template(self) -> str:
        """Template for Solana token tools."""
        return """
async def solana_token_{contract_address}(query: str) -> dict:
    '''Analyze Solana token at {contract_address}'''
    
    try:
        return {{
            "mint_address": "{contract_address}",
            "token_name": "Solana Token",
            "supply": "Unknown",
            "status": "analysis_complete"
        }}
    except Exception as e:
        return {{"error": str(e), "status": "failed"}}
"""
    
    def get_tool_statistics(self) -> Dict[str, Any]:
        """Get statistics about generated tools."""
        return {
            "total_tools": len(self.generated_tools),
            "validated_tools": len([t for t in self.generated_tools.values() if t.validation_status == "validated"]),
            "cached_tools": len(self.tool_cache),
            "average_success_rate": sum(t.success_rate for t in self.generated_tools.values()) / len(self.generated_tools) if self.generated_tools else 0,
            "risk_distribution": {
                "low": len([t for t in self.generated_tools.values() if t.risk_level == "low"]),
                "medium": len([t for t in self.generated_tools.values() if t.risk_level == "medium"]),
                "high": len([t for t in self.generated_tools.values() if t.risk_level == "high"])
            }
        }
