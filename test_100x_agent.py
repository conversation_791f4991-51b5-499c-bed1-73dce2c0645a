#!/usr/bin/env python3
"""
Test Script for 100x Enhanced Agent with Swarm Intelligence
Demonstrates the revolutionary capabilities of the enhanced agent.
"""

import asyncio
import os
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

async def test_100x_agent():
    """Test the 100x enhanced agent capabilities."""
    
    print("🚀 INITIALIZING 100x ENHANCED CRYPTO AGENT")
    print("=" * 60)
    
    try:
        # Import the enhanced agent
        from enhanced_agent import EnhancedLangGraphMCPAgent
        
        # Create agent with swarm mode enabled
        print("📡 Creating enhanced agent with swarm intelligence...")
        agent = EnhancedLangGraphMCPAgent(enable_swarm_mode=True)
        
        # Initialize the agent
        print("🔧 Initializing agent systems...")
        await agent.initialize()
        
        print("\n✅ 100x Enhanced Agent Ready!")
        print(f"🧠 Swarm Mode: {'Enabled' if agent.enable_swarm_mode else 'Disabled'}")
        print(f"🎯 CEO-Agent: {'Active' if agent.ceo_agent else 'Not Available'}")
        print(f"🔧 Available Tools: {len(agent.tools)}")
        
        # Test cases for different specialist areas
        test_cases = [
            {
                "name": "🔍 Crypto Forensics Test",
                "query": "Investigate this suspicious wallet for potential security risks: ******************************************",
                "expected_specialist": "crypto_forensics"
            },
            {
                "name": "💰 DeFi Yield Optimization Test", 
                "query": "Find the best yield farming opportunities with moderate risk for $10,000 USDC",
                "expected_specialist": "defi_yield"
            },
            {
                "name": "🎨 NFT Market Analysis Test",
                "query": "Analyze the Bored Ape Yacht Club collection for potential breakout signals",
                "expected_specialist": "nft_market"
            },
            {
                "name": "🌐 Multi-Domain Strategic Test",
                "query": "Provide comprehensive crypto investment strategy considering DeFi yields, NFT markets, and security risks",
                "expected_specialist": "multi_specialist"
            }
        ]
        
        # Run test cases
        for i, test_case in enumerate(test_cases, 1):
            print(f"\n{'-' * 60}")
            print(f"TEST {i}: {test_case['name']}")
            print(f"Query: {test_case['query']}")
            print(f"Expected Routing: {test_case['expected_specialist']}")
            print(f"{'-' * 60}")
            
            try:
                # Test the enhanced agent
                result = await agent.invoke(
                    test_case['query'], 
                    execution_profile="balanced"
                )
                
                # Display results
                print(f"✅ Analysis Complete!")
                print(f"🎯 Intent: {result.get('intent', 'Unknown')}")
                print(f"🔄 Execution Mode: {result.get('execution_mode', 'Unknown')}")
                print(f"🧠 Reasoning Steps: {len(result.get('reasoning_history', []))}")
                
                # Check if swarm coordination was used
                swarm_info = result.get('swarm_coordination', {})
                if swarm_info.get('coordination_used'):
                    print(f"🚀 Swarm Coordination: SUCCESS")
                    specialists = swarm_info.get('specialists_involved', {})
                    if specialists:
                        print(f"👥 Specialists Involved: {list(specialists.keys())}")
                else:
                    print(f"🔧 Standard Processing Used")
                
                # Show final response preview
                final_response = result.get('final_response', '')
                preview = final_response[:200] + "..." if len(final_response) > 200 else final_response
                print(f"📊 Response Preview: {preview}")
                
            except Exception as e:
                print(f"❌ Test Failed: {e}")
                continue
        
        print(f"\n{'=' * 60}")
        print("🎉 100x ENHANCED AGENT TESTING COMPLETE!")
        print("🚀 Revolutionary capabilities demonstrated:")
        print("   ✅ Agent Swarms with Specialist Coordination")
        print("   ✅ Dynamic Query Routing")
        print("   ✅ Multi-Domain Intelligence")
        print("   ✅ Sophisticated State Management")
        print("   ✅ Fallback Resilience")
        
        # Cleanup
        await agent.cleanup()
        
    except ImportError as e:
        print(f"❌ Import Error: {e}")
        print("💡 Make sure all dependencies are installed:")
        print("   pip install langchain langgraph langchain-openai python-dotenv")
        
    except Exception as e:
        print(f"❌ Unexpected Error: {e}")
        print("🔧 Check your environment variables and API keys")


async def test_specialist_routing():
    """Test the specialist routing logic."""
    
    print("\n🧪 TESTING SPECIALIST ROUTING LOGIC")
    print("=" * 50)
    
    try:
        from enhanced_agent import EnhancedLangGraphMCPAgent
        
        # Create agent for testing routing logic
        agent = EnhancedLangGraphMCPAgent(enable_swarm_mode=True)
        
        # Test routing decisions
        test_queries = [
            ("trace suspicious transactions", "Should route to Crypto Forensics"),
            ("best yield farming protocols", "Should route to DeFi Yield"),
            ("NFT collection floor price analysis", "Should route to NFT Market"),
            ("simple wallet balance check", "Should use direct execution"),
            ("complex multi-domain strategy", "Should use swarm coordination")
        ]
        
        for query, expected in test_queries:
            should_use_swarm = agent._should_use_swarm_coordination(query, "mixed_query")
            print(f"Query: '{query}'")
            print(f"Expected: {expected}")
            print(f"Swarm Routing: {'YES' if should_use_swarm else 'NO'}")
            print("-" * 30)
            
    except Exception as e:
        print(f"❌ Routing Test Failed: {e}")


if __name__ == "__main__":
    print("🌟 100x ENHANCED CRYPTO AGENT - REVOLUTIONARY TESTING")
    print("🚀 Demonstrating Agent Swarms, Dynamic Routing, and Multi-Domain Intelligence")
    print()
    
    # Run the tests
    asyncio.run(test_100x_agent())
    asyncio.run(test_specialist_routing())
    
    print("\n🎯 NEXT STEPS FOR FULL 100x IMPLEMENTATION:")
    print("1. ✅ Phase 1: Agent Swarms - IMPLEMENTED")
    print("2. 🔄 Phase 2: Dynamic Graph Recompilation")
    print("3. 🔄 Phase 3: Multi-Modal Fusion")
    print("4. 🔄 Phase 4: Persistent Memory & Autonomy")
    print("\n💡 This demonstrates the foundation for 100x improvements!")
