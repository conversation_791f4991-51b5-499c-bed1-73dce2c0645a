#!/usr/bin/env python3
"""
100x Enhanced LangGraph MCP Agent with Agent Swarms & Self-Improvement
Revolutionary agent architecture with multi-agent swarms, dynamic graph recompilation,
multi-modal fusion, generative tooling, and autonomous operation.
"""

import os
import re
import asyncio
import json
import time
from typing import Dict, Any, List, TypedDict, Annotated, Optional, Literal, Union
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from dotenv import load_dotenv

from langchain_openai import ChatOpenAI
from langchain_mcp_adapters.client import MultiServerMCPClient
from langgraph.graph import StateGraph, END, START
from langgraph.graph.message import add_messages
from langgraph.prebuilt import ToolNode, create_react_agent
from langgraph.checkpoint.memory import MemorySaver
from langgraph.types import Command
from langchain_core.messages import HumanMessage, AIMessage, ToolMessage
from langchain_core.runnables import RunnableConfig
from langchain_core.tools import tool, InjectedToolCallId
from langgraph.prebuilt import InjectedState

from mcp_config import get_safe_mcp_configs, print_server_info, clean_config_for_mcp

# Load environment variables
load_dotenv()

@dataclass
class PerformanceMetrics:
    """Track agent performance for self-improvement."""
    execution_times: List[float] = field(default_factory=list)
    success_rates: Dict[str, float] = field(default_factory=dict)
    loop_counts: Dict[str, int] = field(default_factory=dict)
    tool_effectiveness: Dict[str, float] = field(default_factory=dict)
    reasoning_patterns: List[str] = field(default_factory=list)
    graph_version: str = "v1.0"
    last_optimization: datetime = field(default_factory=datetime.now)

@dataclass
class MultiModalContext:
    """Store multi-modal analysis results."""
    image_analysis: Dict[str, Any] = field(default_factory=dict)
    audio_summary: Dict[str, Any] = field(default_factory=dict)
    video_analysis: Dict[str, Any] = field(default_factory=dict)
    cross_modal_insights: List[str] = field(default_factory=list)

class AgentState(TypedDict):
    """100x Enhanced state object with swarm intelligence and self-improvement."""
    # Core input/output
    input: str
    intent: str
    final_response: str

    # Planning and reasoning
    plan: List[str]
    current_step: int
    reasoning_history: List[str]

    # Tool execution tracking
    messages: Annotated[list, add_messages]
    tool_outputs: List[dict]

    # Progress tracking
    information_gathered: dict
    next_action: str  # "direct_execute", "plan_and_reflect", "delegate_to_specialist", "synthesize", "complete"
    confidence_level: float

    # Complexity and execution mode
    complexity_level: str  # "simple", "moderate", "complex"
    execution_mode: str    # "direct", "multi_step"
    execution_profile: str  # "fast", "balanced", "thorough"

    # Dynamic reasoning enhancements
    last_tool_output_quality: str  # "good", "bad", "insufficient", "complete"
    error_count: int
    reflection_depth: int

    # Loop prevention
    planning_cycles: int

    # 100x ENHANCEMENTS
    # Agent Swarms
    active_specialist: str  # Current specialist agent handling the task
    specialist_context: dict  # Context passed between specialists
    swarm_coordination: dict  # Inter-agent communication state

    # Dynamic Graph Recompilation
    performance_metrics: PerformanceMetrics
    graph_optimizations: List[str]  # Applied optimizations
    meta_reflection_triggered: bool

    # Multi-Modal Fusion
    multi_modal_context: MultiModalContext

    # Generative Tooling
    generated_tools: Dict[str, Any]  # Cache of dynamically created tools
    tool_generation_history: List[str]
    sandbox_results: Dict[str, Any]

    # Human-in-the-Loop
    pending_approvals: List[Dict[str, Any]]
    user_preferences: Dict[str, Any]
    risk_tolerance: str  # "conservative", "moderate", "aggressive"

    # Persistent Memory & Learning
    memory_context: Dict[str, Any]  # Retrieved similar experiences
    learning_insights: List[str]
    performance_history: Dict[str, Any]

    # Continuous Operation
    autonomous_goals: List[Dict[str, Any]]
    scheduled_tasks: List[Dict[str, Any]]
    monitoring_targets: List[str]

    # Speculative Execution
    speculative_results: Dict[str, Any]
    parallel_executions: List[str]
    model_tier_used: str  # "fast", "balanced", "premium"

class EnhancedLangGraphMCPAgent:
    """100x Enhanced LangGraph agent with swarm intelligence and self-improvement."""

    def __init__(self, enable_swarm_mode: bool = True):
        self.model = None
        self.client = None
        self.tools = []
        self.graph = None

        # 100x ENHANCEMENTS
        self.enable_swarm_mode = enable_swarm_mode
        self.ceo_agent = None  # Will be initialized after base setup
        self.performance_tracker = None  # Will be initialized with proper tracker
        self.graph_recompiler = None  # Dynamic graph recompilation engine
        self.graph_versions = {}  # Store different graph optimizations
        self.generated_tools_cache = {}
        self.memory_store = None  # Vector database connection
        self.autonomous_mode = False
        
    def _setup_model(self):
        """Setup the language model using OpenRouter with Claude Sonnet 4."""
        api_key = os.getenv("OPENROUTER_API_KEY")
        model_name = os.getenv("OPENROUTER_MODEL", "anthropic/claude-3.5-sonnet")

        if not api_key:
            raise ValueError("OPENROUTER_API_KEY not found in environment variables")

        # Configure for Claude Sonnet 4 with optimized settings
        self.model = ChatOpenAI(
            model=model_name,
            openai_api_key=api_key,
            openai_api_base="https://openrouter.ai/api/v1",
            temperature=0.1,
            max_tokens=8000,  # Increased for Claude's larger context
            top_p=0.9,
            frequency_penalty=0.0,
            presence_penalty=0.0
        )

        print(f"✅ Model configured: {model_name} (Claude Sonnet 4)")
    
    def _setup_mcp_client(self, server_configs: Dict[str, Dict[str, Any]]):
        """Setup MCP client with server configurations."""
        self.client = MultiServerMCPClient(server_configs)
        print(f"✅ MCP client configured with {len(server_configs)} server(s)")
    
    async def initialize(self, server_configs: Dict[str, Dict[str, Any]] = None):
        """Initialize the enhanced agent with model and MCP tools."""
        
        # Setup model
        self._setup_model()
        
        # Default server configuration if none provided
        if server_configs is None:
            server_configs = get_safe_mcp_configs()
            print_server_info(server_configs)
        
        # Clean configuration for MCP client (remove display fields)
        clean_configs = clean_config_for_mcp(server_configs)
        
        # Setup MCP client
        self._setup_mcp_client(clean_configs)
        
        # Get tools from MCP servers with error handling
        try:
            self.tools = await self.client.get_tools()
            print(f"✅ Loaded {len(self.tools)} tools from MCP servers")
            
            # Print tool summary by server
            tool_names = [tool.name for tool in self.tools]
            print(f"🔧 Available tools: {', '.join(tool_names[:10])}" + 
                  (f" and {len(tool_names)-10} more..." if len(tool_names) > 10 else ""))
                  
        except Exception as e:
            print(f"⚠️  Warning: Some MCP servers may not be available: {e}")
            print("🔄 Retrying with minimal configuration...")
            
            # Fallback to minimal configuration
            from mcp_config import MINIMAL_CONFIG
            clean_minimal = clean_config_for_mcp(MINIMAL_CONFIG)
            self._setup_mcp_client(clean_minimal)
            self.tools = await self.client.get_tools()
            print(f"✅ Loaded {len(self.tools)} tools from local server only")
        
        # Build the enhanced graph
        self._build_graph()

        # Initialize performance tracking and graph recompilation
        try:
            from performance_tracker import PerformanceTracker
            from graph_recompiler import GraphRecompiler

            self.performance_tracker = PerformanceTracker()
            self.graph_recompiler = GraphRecompiler(self)
            print("✅ Dynamic graph recompilation system initialized")
        except ImportError as e:
            print(f"⚠️  Graph recompilation disabled: {e}")

        # Initialize Phase 3: Enhanced Capabilities
        try:
            from multi_modal_processor import MultiModalProcessor
            from generative_tooling import GenerativeToolingEngine
            from human_in_the_loop import HumanInTheLoopSystem

            self.multi_modal_processor = MultiModalProcessor(self.openai_api_key)
            self.generative_tooling = GenerativeToolingEngine(self.openai_api_key)
            self.human_in_the_loop = HumanInTheLoopSystem()
            print("✅ Phase 3 Enhanced Capabilities initialized")
            print("   🖼️  Multi-Modal Fusion: Ready")
            print("   🔧 Generative Tooling: Ready")
            print("   👤 Human-in-the-Loop: Ready")
        except ImportError as e:
            print(f"⚠️  Phase 3 capabilities disabled: {e}")
            self.multi_modal_processor = None
            self.generative_tooling = None
            self.human_in_the_loop = None

        # Initialize CEO-Agent for swarm coordination (if enabled)
        if self.enable_swarm_mode:
            try:
                from ceo_agent import CEOAgent
                self.ceo_agent = CEOAgent(self)
                print("✅ CEO-Agent swarm coordination initialized")
            except ImportError as e:
                print(f"⚠️  Swarm mode disabled: {e}")
                self.enable_swarm_mode = False

        print("✅ 100x Enhanced LangGraph agent created successfully")
    
    def _build_graph(self):
        """Build the sophisticated multi-step reasoning graph with dynamic reflection."""

        # Create the state graph
        workflow = StateGraph(AgentState)

        # Add nodes with new dynamic architecture
        workflow.add_node("router", self._query_router_node)
        workflow.add_node("direct_executor", self._direct_execution_node)
        workflow.add_node("dynamic_planner", self._dynamic_planning_node)
        workflow.add_node("tools", self._enhanced_tool_node)
        workflow.add_node("reflector", self._reflector_node)
        workflow.add_node("address_specialist", self._process_with_react_agent_node)
        workflow.add_node("synthesizer", self._synthesis_node)

        # 100x ENHANCEMENT: Add swarm coordination node
        workflow.add_node("swarm_coordinator", self._swarm_coordination_node)

        # 100x ENHANCEMENT: Add meta-reflector node for graph optimization
        workflow.add_node("meta_reflector", self._meta_reflector_node)

        # PHASE 3: Enhanced Capabilities nodes
        workflow.add_node("multi_modal_analyzer", self._multi_modal_analysis_node)
        workflow.add_node("tool_generator", self._tool_generation_node)
        workflow.add_node("approval_gateway", self._approval_gateway_node)

        # Define the flow
        workflow.set_entry_point("router")

        # Conditional routing from router to different execution paths
        workflow.add_conditional_edges(
            "router",
            lambda state: state.get("next_action"),
            {
                "direct_execute": "direct_executor",
                "plan_and_reflect": "dynamic_planner",
                "delegate_to_specialist": "address_specialist",
                "delegate_to_swarm": "swarm_coordinator"
            }
        )

        # Direct execution path
        workflow.add_edge("direct_executor", END)

        # Specialist execution path
        workflow.add_edge("address_specialist", END)

        # Swarm coordination path
        workflow.add_edge("swarm_coordinator", END)

        # Dynamic planning and reflection loop with Phase 3 enhancements
        workflow.add_edge("dynamic_planner", "multi_modal_analyzer")
        workflow.add_edge("multi_modal_analyzer", "tool_generator")
        workflow.add_edge("tool_generator", "approval_gateway")
        workflow.add_edge("approval_gateway", "tools")
        workflow.add_edge("tools", "reflector")

        # Conditional routing from reflector
        workflow.add_conditional_edges(
            "reflector",
            lambda state: "synthesizer" if state["last_tool_output_quality"] == "complete" else "dynamic_planner",
            {
                "dynamic_planner": "dynamic_planner",  # Loop back for more planning
                "synthesizer": "synthesizer"           # Complete and synthesize
            }
        )

        # 100x ENHANCEMENT: Add meta-reflector routing for graph optimization
        workflow.add_conditional_edges(
            "synthesizer",
            lambda state: "optimize" if (
                self.graph_recompiler and
                self.performance_tracker and
                self.performance_tracker.should_trigger_optimization(state)
            ) else "complete",
            {
                "optimize": "meta_reflector",
                "complete": END
            }
        )

        # Meta-reflector completes to END after optimization
        workflow.add_edge("meta_reflector", END)

        # Compile the graph
        self.graph = workflow.compile()

        # Also create a React agent for proper MCP tool execution
        self._create_react_agent()

        print("✅ Enhanced dynamic reasoning graph compiled successfully")

    async def _process_with_react_agent_node(self, state: AgentState) -> AgentState:
        """Process address analysis using the React agent as a graph node."""
        user_input = state.get("input", "")

        # Extract address info from the input
        address_info = self._analyze_crypto_address(user_input)

        try:
            # Process with React agent (already async, no need for asyncio.run)
            result = await self._process_with_react_agent(user_input, address_info)

            return {
                **state,
                "final_response": result,
                "next_action": "complete"
            }
        except Exception as e:
            # Fallback to synthesis with error message
            return {
                **state,
                "final_response": f"Address analysis failed: {e}. Please try again or use a different approach.",
                "next_action": "complete"
            }

    def _create_react_agent(self):
        """Create a React agent for proper MCP tool execution."""
        if self.tools:
            prompt = (
                "You are an expert blockchain and cryptocurrency analysis agent with access to 114 comprehensive tools. "
                "Your mission is to provide DETAILED, PROFESSIONAL-GRADE analysis that rivals top crypto research firms. "

                "CORE PRINCIPLES: "
                "1. EXECUTE ALL STEPS COMPLETELY - Never stop early or skip steps "
                "2. USE MULTIPLE TOOLS - Gather comprehensive data from all relevant sources "
                "3. PROVIDE SPECIFIC NUMBERS - Include exact values, percentages, and calculations "
                "4. PROFESSIONAL ANALYSIS - Write like a senior crypto analyst "
                "5. ACTIONABLE INSIGHTS - Include clear recommendations and risk assessments "

                "FOR SOLANA ADDRESSES: "
                "- Always start with solana_gettokenmetadata to determine if it's a token or wallet "
                "- For tokens: Execute ALL 5 steps (metadata, price, pairs, swaps, holders) "
                "- For wallets: Execute ALL 9 steps (portfolio, balance, trading history, etc.) "
                "- Use solana_gettopholders for detailed holder analysis "
                "- Calculate portfolio values and percentages "

                "FOR ETHEREUM ADDRESSES: "
                "- Use evm_getwallettokenbalancesprice for complete portfolio analysis "
                "- Use evm_getwalletnfts for NFT holdings "
                "- Use evm_getwallethistory for transaction analysis "

                "ANALYSIS QUALITY STANDARDS: "
                "- Include current USD values for all holdings "
                "- Calculate portfolio allocation percentages "
                "- Identify trading patterns and strategies "
                "- Assess risk levels and concentration "
                "- Provide clear investment recommendations "
                "- Format output professionally with clear sections "

                "Remember: You have access to real-time blockchain data. Use it to provide the most accurate and comprehensive analysis possible."
            )

            self.react_agent = create_react_agent(
                self.model,
                self.tools,
                prompt=prompt,
                checkpointer=MemorySaver()
            )
            print("✅ React agent created for MCP tool execution")
    
    def _assess_query_complexity(self, user_input: str, intent: str, address_info: dict) -> tuple:
        """Assess query complexity and determine execution mode."""

        # Simple patterns that should use direct execution
        simple_patterns = [
            r"^(check|get|show|what.*is.*the)\s+(balance|nft|token|price)",
            r"^(balance|nft|tokens?)\s+(of|for)",
            r"^\d+\s*[\+\-\*\/]\s*\d+",  # Simple math
            r"^what.*is.*\d+.*[\+\-\*\/]",  # Math questions
            r"^search\s+for\s+[^,]+$",  # Simple search without additional requests
            r"^(get|show|list)\s+(nft|token|balance)",
        ]

        # Complex patterns that need multi-step reasoning
        complex_patterns = [
            r"analyz|recommend|strateg|insight|advice|suggest",
            r"compare.*and|versus|vs\.|investment",
            r"portfolio.*optim|risk.*assess|due.*diligence",
            r"should.*i|what.*do.*with|how.*to.*invest",
            r".*and.*(analyz|recommend|strateg)",  # Multiple actions
        ]

        # Check for simple patterns
        for pattern in simple_patterns:
            if re.search(pattern, user_input.lower()):
                return "simple", "direct"

        # Check for complex patterns
        for pattern in complex_patterns:
            if re.search(pattern, user_input.lower()):
                return "complex", "multi_step"

        # Context-based complexity assessment
        words = user_input.lower().split()

        # Simple if:
        # - Single address with clear action context
        if address_info["found"] and any(action in user_input.lower() for action in
                                       ["balance", "nft", "token", "check", "get", "show"]):
            return "simple", "direct"

        # - Short, specific requests
        if len(words) <= 5 and intent in ["calculation", "blockchain_analysis", "web_research"]:
            return "simple", "direct"

        # Complex if:
        # - Address with no context (needs investigation)
        if address_info["found"] and len(words) <= 2:
            return "complex", "multi_step"

        # - Multiple intents or long queries
        if intent == "mixed_query" or len(words) > 10:
            return "complex", "multi_step"

        # - Strategic thinking intent
        if intent == "strategic_thinking":
            return "complex", "multi_step"

        # Default to moderate complexity
        return "moderate", "multi_step"

    def _query_router_node(self, state: AgentState) -> AgentState:
        """Analyze user input to determine intent, complexity, and execution mode with specialist routing."""
        user_input = state.get("input", "").strip()
        execution_profile = state.get("execution_profile", "balanced")

        # Analyze crypto addresses
        address_info = self._analyze_crypto_address(user_input)

        # Determine intent
        intent = self._determine_intent(user_input, address_info)

        # Assess complexity and execution mode with profile influence
        complexity_level, execution_mode = self._assess_query_complexity(user_input, intent, address_info)

        # Let execution profile influence routing decisions
        if execution_profile == 'fast' and complexity_level == "simple":
            execution_mode = "direct"
        elif execution_profile == 'thorough':
            # Thorough mode prefers multi-step even for moderate complexity
            execution_mode = "multi_step"

        # 100x ENHANCEMENT: Check if swarm coordination is needed
        if self.enable_swarm_mode and self._should_use_swarm_coordination(user_input, intent):
            next_action = "delegate_to_swarm"
            plan = ["Delegate to CEO-Agent", "Coordinate specialists", "Synthesize results"]
        # Determine next action based on intent and execution mode
        elif intent == "address_analysis":
            # For address analysis, use multi-step approach for comprehensive analysis
            next_action = "plan_and_reflect"
            plan = self._create_comprehensive_address_plan(user_input, address_info)
        elif execution_mode == "direct":
            next_action = "direct_execute"
            plan = self._create_direct_plan(intent, user_input, address_info)
        else:
            next_action = "plan_and_reflect"
            plan = self._create_multi_step_plan(intent, user_input, address_info)

        # Set confidence based on clarity and complexity
        confidence = self._calculate_confidence(intent, complexity_level, address_info)

        reasoning = f"Intent: {intent}, Complexity: {complexity_level}, Mode: {execution_mode}, Profile: {execution_profile}"

        return {
            **state,
            "intent": intent,
            "plan": plan,
            "complexity_level": complexity_level,
            "execution_mode": execution_mode,
            "confidence_level": confidence,
            "current_step": 0,
            "planning_cycles": 0,
            "reasoning_history": [reasoning],
            "information_gathered": {},
            "next_action": next_action,
            "messages": [HumanMessage(content=user_input)]
        }
    
    def _determine_intent(self, user_input: str, address_info: dict) -> str:
        """Determine the primary intent of the user query."""

        # Address-only input
        if address_info["found"] and len(user_input.split()) <= 2:
            return "address_analysis"

        # Standard intent analysis patterns
        intent_patterns = {
            "blockchain_analysis": [
                r"wallet|address|nft|token|balance|transaction|defi|portfolio",
                r"0x[a-fA-F0-9]{40}|[13][a-km-zA-HJ-NP-Z1-9]{25,34}",
                r"analyze.*wallet|get.*nft|check.*balance"
            ],
            "web_research": [
                r"search|news|find|latest|recent|information|research",
                r"price.*prediction|market.*analysis|crypto.*news"
            ],
            "calculation": [
                r"calculate|compute|math|percentage|multiply|divide|add",
                r"\d+.*[\+\-\*\/].*\d+|what.*is.*\d+"
            ],
            "strategic_thinking": [
                r"recommend|strategy|analyze|think|reasoning|plan",
                r"investment|advice|should.*i|what.*do"
            ]
        }

        # Determine intent
        detected_intents = []
        for intent_type, patterns in intent_patterns.items():
            for pattern in patterns:
                if re.search(pattern, user_input.lower()):
                    detected_intents.append(intent_type)
                    break

        # Classify overall intent
        if len(detected_intents) > 1:
            return "mixed_query"
        elif detected_intents:
            return detected_intents[0]
        else:
            return "general_query"

    def _calculate_confidence(self, intent: str, complexity_level: str, address_info: dict) -> float:
        """Calculate confidence level based on intent clarity and complexity."""
        base_confidence = 0.5

        # Higher confidence for clear intents
        if intent in ["calculation", "blockchain_analysis", "web_research"]:
            base_confidence = 0.8
        elif intent == "address_analysis" and address_info["found"]:
            base_confidence = address_info["confidence"]
        elif intent == "mixed_query":
            base_confidence = 0.7

        # Adjust for complexity
        if complexity_level == "simple":
            base_confidence += 0.1
        elif complexity_level == "complex":
            base_confidence -= 0.1

        return min(0.95, max(0.3, base_confidence))

    def _should_use_swarm_coordination(self, user_input: str, intent: str) -> bool:
        """Determine if the query requires specialist swarm coordination."""
        if not self.enable_swarm_mode or not self.ceo_agent:
            return False

        # Keywords that indicate specialist expertise needed
        specialist_keywords = {
            "forensics": ["trace", "investigate", "security", "exploit", "hack", "suspicious", "fraud"],
            "defi": ["yield", "farming", "liquidity", "apy", "protocol", "defi", "staking"],
            "nft": ["nft", "collection", "floor", "opensea", "rarity", "mint", "pfp"]
        }

        # Check if query contains specialist keywords
        query_lower = user_input.lower()
        for category, keywords in specialist_keywords.items():
            if any(keyword in query_lower for keyword in keywords):
                return True

        # Complex multi-domain queries
        if intent == "mixed_query" and len(user_input.split()) > 15:
            return True

        # Strategic thinking that could benefit from multiple perspectives
        if intent == "strategic_thinking":
            return True

        return False

    async def _swarm_coordination_node(self, state: AgentState) -> AgentState:
        """Coordinate with specialist agents through CEO-Agent."""
        if not self.ceo_agent:
            # Fallback to regular processing if swarm is not available
            return {
                **state,
                "final_response": "Swarm coordination not available, falling back to standard processing.",
                "next_action": "plan_and_reflect"
            }

        try:
            # Delegate to CEO-Agent for swarm coordination
            user_input = state.get("input", "")
            execution_profile = state.get("execution_profile", "balanced")

            print(f"🚀 Delegating to CEO-Agent swarm: {user_input[:100]}...")

            # Use CEO-Agent coordination
            swarm_result = await self.ceo_agent.coordinate(user_input, execution_profile)

            # Extract the final response from swarm coordination
            final_response = swarm_result.get("final_response", "Swarm coordination completed")

            return {
                **state,
                "final_response": final_response,
                "swarm_coordination": {
                    "coordination_used": True,
                    "specialists_involved": swarm_result.get("specialist_context", {}),
                    "coordination_success": True
                },
                "reasoning_history": state.get("reasoning_history", []) +
                                   ["Swarm coordination completed successfully"],
                "next_action": "complete"
            }

        except Exception as e:
            print(f"⚠️  Swarm coordination failed: {e}")
            # Fallback to regular processing
            return {
                **state,
                "swarm_coordination": {
                    "coordination_used": False,
                    "error": str(e),
                    "fallback_used": True
                },
                "reasoning_history": state.get("reasoning_history", []) +
                                   [f"Swarm coordination failed, using fallback: {e}"],
                "next_action": "plan_and_reflect"
            }

    async def _meta_reflector_node(self, state: AgentState) -> AgentState:
        """Meta-reflector node that analyzes performance and optimizes the graph."""

        if not self.graph_recompiler or not self.performance_tracker:
            # Skip optimization if systems not available
            return {
                **state,
                "reasoning_history": state.get("reasoning_history", []) +
                                   ["Meta-reflection skipped: systems not available"],
                "meta_reflection_triggered": False
            }

        try:
            print("🧠 Meta-Reflector: Analyzing agent performance...")

            # Record current query performance
            execution_time = time.time() - state.get("start_time", time.time())
            success = state.get("confidence_level", 0.0) > 0.7

            self.performance_tracker.record_query_pattern(state, success, execution_time)

            # Trigger graph optimization check
            optimized_state = await self.graph_recompiler.check_and_optimize_graph(state)

            return {
                **optimized_state,
                "reasoning_history": optimized_state.get("reasoning_history", []) +
                                   ["Meta-reflection completed: graph optimization analyzed"],
                "meta_reflection_triggered": True
            }

        except Exception as e:
            print(f"⚠️  Meta-reflection failed: {e}")
            return {
                **state,
                "reasoning_history": state.get("reasoning_history", []) +
                                   [f"Meta-reflection failed: {e}"],
                "meta_reflection_triggered": False
            }

    async def _multi_modal_analysis_node(self, state: AgentState) -> AgentState:
        """Phase 3: Multi-modal analysis node for processing images, audio, and video."""

        if not self.multi_modal_processor:
            return {
                **state,
                "reasoning_history": state.get("reasoning_history", []) +
                                   ["Multi-modal analysis skipped: processor not available"]
            }

        try:
            user_input = state.get("input", "")
            multi_modal_context = state.get("multi_modal_context", MultiModalContext())

            print("🖼️  Analyzing multi-modal content...")

            # Check if input contains image URLs or references
            if any(ext in user_input.lower() for ext in ['.jpg', '.png', '.gif', 'image', 'chart', 'screenshot']):
                # Simulate image analysis (in real implementation, would extract URLs/files)
                image_result = await self.multi_modal_processor.analyze_image(
                    "placeholder_image_url",
                    analysis_type="crypto_chart"
                )
                multi_modal_context.image_analysis["chart_analysis"] = image_result

                print(f"   📊 Chart Analysis: {image_result.sentiment} sentiment")

            # Check for audio/video content references
            if any(term in user_input.lower() for term in ['video', 'audio', 'podcast', 'ama', 'youtube']):
                print("   🎵 Audio/Video analysis capabilities available")
                multi_modal_context.cross_modal_insights.append(
                    "Multi-modal content detected - enhanced analysis possible"
                )

            return {
                **state,
                "multi_modal_context": multi_modal_context,
                "reasoning_history": state.get("reasoning_history", []) +
                                   ["Multi-modal analysis completed"],
                "confidence_level": min(1.0, state.get("confidence_level", 0.0) + 0.1)
            }

        except Exception as e:
            print(f"⚠️  Multi-modal analysis failed: {e}")
            return {
                **state,
                "reasoning_history": state.get("reasoning_history", []) +
                                   [f"Multi-modal analysis failed: {e}"]
            }

    async def _tool_generation_node(self, state: AgentState) -> AgentState:
        """Phase 3: Dynamic tool generation for unknown protocols."""

        if not self.generative_tooling:
            return {
                **state,
                "reasoning_history": state.get("reasoning_history", []) +
                                   ["Tool generation skipped: engine not available"]
            }

        try:
            user_input = state.get("input", "")

            # Check if input mentions unknown contracts or protocols
            contract_patterns = [
                r'0x[a-fA-F0-9]{40}',  # Ethereum address
                r'[1-9A-HJ-NP-Za-km-z]{32,44}'  # Solana address (simplified)
            ]

            import re
            for pattern in contract_patterns:
                matches = re.findall(pattern, user_input)
                if matches:
                    contract_address = matches[0]

                    print(f"🔧 Generating tool for contract: {contract_address[:10]}...")

                    # Create tool generation request
                    from generative_tooling import ToolGenerationRequest
                    request = ToolGenerationRequest(
                        contract_address=contract_address,
                        blockchain="ethereum" if pattern.startswith('0x') else "solana",
                        operation_type="read",
                        user_intent=user_input
                    )

                    # Generate tool
                    generated_tool = await self.generative_tooling.generate_tool(request)

                    if generated_tool:
                        # Store in state
                        generated_tools = state.get("generated_tools", {})
                        generated_tools[contract_address] = generated_tool

                        print(f"   ✅ Tool generated: {generated_tool.name}")

                        return {
                            **state,
                            "generated_tools": generated_tools,
                            "tool_generation_history": state.get("tool_generation_history", []) +
                                                     [f"Generated tool for {contract_address}"],
                            "reasoning_history": state.get("reasoning_history", []) +
                                               ["Dynamic tool generation completed"],
                            "confidence_level": min(1.0, state.get("confidence_level", 0.0) + 0.15)
                        }

            return {
                **state,
                "reasoning_history": state.get("reasoning_history", []) +
                                   ["No tool generation needed"]
            }

        except Exception as e:
            print(f"⚠️  Tool generation failed: {e}")
            return {
                **state,
                "reasoning_history": state.get("reasoning_history", []) +
                                   [f"Tool generation failed: {e}"]
            }

    async def _approval_gateway_node(self, state: AgentState) -> AgentState:
        """Phase 3: Human-in-the-loop approval gateway for high-risk operations."""

        if not self.human_in_the_loop:
            return {
                **state,
                "reasoning_history": state.get("reasoning_history", []) +
                                   ["Approval gateway skipped: system not available"]
            }

        try:
            # Assess if approval is needed
            risk_indicators = []

            # Check for high-value operations
            user_input = state.get("input", "").lower()
            if any(term in user_input for term in ['buy', 'sell', 'trade', 'swap', 'invest']):
                risk_indicators.append("financial_operation")

            # Check for experimental tools
            if state.get("generated_tools"):
                risk_indicators.append("experimental_tool_usage")

            # Check for new protocols
            if any(term in user_input for term in ['new', 'unknown', 'experimental', 'beta']):
                risk_indicators.append("new_protocol")

            if risk_indicators:
                print("👤 High-risk operation detected - requesting approval...")

                # Create approval request
                operation_data = {
                    "type": "crypto_analysis",
                    "description": f"Crypto analysis operation: {state.get('input', '')[:100]}",
                    "risk_factors": risk_indicators,
                    "impact": "Analysis and potential recommendations",
                    "experimental_tool": bool(state.get("generated_tools")),
                    "new_protocol": "new_protocol" in risk_indicators
                }

                # Request approval (in demo mode, auto-approve for testing)
                approval_request = await self.human_in_the_loop.request_approval(operation_data)

                # Store approval in state
                pending_approvals = state.get("pending_approvals", [])
                pending_approvals.append({
                    "request_id": approval_request.request_id,
                    "status": approval_request.status.value,
                    "operation": operation_data
                })

                if approval_request.status.value == "approved":
                    print("   ✅ Operation approved")
                    return {
                        **state,
                        "pending_approvals": pending_approvals,
                        "reasoning_history": state.get("reasoning_history", []) +
                                           ["Human approval obtained"],
                        "confidence_level": min(1.0, state.get("confidence_level", 0.0) + 0.1)
                    }
                else:
                    print(f"   ❌ Operation {approval_request.status.value}")
                    return {
                        **state,
                        "pending_approvals": pending_approvals,
                        "final_response": f"Operation {approval_request.status.value} by user approval system",
                        "reasoning_history": state.get("reasoning_history", []) +
                                           [f"Operation {approval_request.status.value} by approval gateway"]
                    }

            return {
                **state,
                "reasoning_history": state.get("reasoning_history", []) +
                                   ["No approval required - low risk operation"]
            }

        except Exception as e:
            print(f"⚠️  Approval gateway failed: {e}")
            return {
                **state,
                "reasoning_history": state.get("reasoning_history", []) +
                                   [f"Approval gateway failed: {e}"]
            }

    def _create_direct_plan(self, intent: str, user_input: str, address_info: dict) -> List[str]:
        """Create a simple, direct execution plan."""

        if intent == "calculation":
            return ["Perform calculation", "Return result"]

        elif intent == "blockchain_analysis":
            if address_info["found"]:
                blockchain = address_info.get("blockchain", "ethereum")
                if "balance" in user_input.lower():
                    return [f"Get {blockchain} wallet balance"]
                elif "nft" in user_input.lower():
                    return [f"Get {blockchain} NFT collection"]
                elif "token" in user_input.lower():
                    return [f"Get {blockchain} token holdings"]
                else:
                    return [f"Get {blockchain} wallet overview"]
            else:
                return ["Get blockchain data"]

        elif intent == "web_research":
            return ["Search for information", "Return results"]

        else:
            return ["Execute request", "Return response"]

    def _create_multi_step_plan(self, intent: str, user_input: str, address_info: dict) -> List[str]:
        """Create a comprehensive multi-step plan for complex queries."""

        if intent == "address_analysis":
            return self._create_address_analysis_plan(address_info)

        elif intent == "mixed_query":
            return [
                "Break down the complex query",
                "Gather web research data",
                "Collect blockchain data",
                "Apply comprehensive strategic analysis",
                "Synthesize multi-source response"
            ]

        elif intent == "strategic_thinking":
            return [
                "Gather relevant data",
                "Apply sequential thinking for deep analysis",
                "Generate strategic recommendations"
            ]

        elif intent == "blockchain_analysis":
            return [
                "Extract wallet addresses or contract addresses",
                "Gather comprehensive blockchain data",
                "Apply strategic analysis to understand patterns",
                "Provide comprehensive insights"
            ]

        elif intent == "web_research":
            return [
                "Search for relevant information",
                "Extract key findings",
                "Apply analysis to synthesize insights",
                "Summarize results with context"
            ]

        else:
            return ["Analyze request", "Apply reasoning", "Provide response"]

    def _create_address_analysis_plan(self, address_info: dict) -> List[str]:
        """Create comprehensive analysis plan for crypto addresses."""
        blockchain = address_info.get("blockchain", "unknown")
        address = address_info.get("address", "")

        if blockchain == "solana":
            # Solana addresses could be wallets OR token contracts - comprehensive analysis
            return [
                f"Step 1: Get token metadata for {address} (check if it's a token contract)",
                "Step 2: Get token price and market data if it's a token",
                "Step 3: Analyze trading pairs and liquidity distribution",
                "Step 4: Examine recent trading activity and patterns",
                "Step 5: Analyze holder distribution and concentration",
                "Step 6: If not a token, get comprehensive wallet portfolio",
                "Step 7: Verify native SOL balance",
                "Step 8: Analyze wallet trading history and patterns",
                "Step 9: Apply strategic analysis to synthesize findings",
                "Step 10: Provide comprehensive token/wallet analysis with insights"
            ]
        elif blockchain == "ethereum":
            return [
                f"Step 1: Determine if {address} is wallet, token contract, or NFT contract",
                "Step 2: Get appropriate data (wallet balance, token info, or NFT collection)",
                "Step 3: Analyze transaction history and patterns",
                "Step 4: Check for DeFi positions if wallet",
                "Step 5: Apply strategic analysis",
                "Step 6: Provide comprehensive summary"
            ]
        else:
            return [
                f"Step 1: Analyze {blockchain} address {address}",
                "Step 2: Determine address type (wallet/contract/token)",
                "Step 3: Gather appropriate data based on type",
                "Step 4: Apply strategic analysis",
                "Step 5: Provide comprehensive summary"
            ]

    def _create_initial_plan(self, intent: str, user_input: str) -> List[str]:
        """Create initial plan based on detected intent."""
        plans = {
            "blockchain_analysis": [
                "Extract wallet addresses or contract addresses",
                "Gather blockchain data (NFTs, tokens, transactions)",
                "Apply strategic analysis to understand patterns",
                "Provide comprehensive insights"
            ],
            "web_research": [
                "Search for relevant information",
                "Extract key findings",
                "Apply analysis to synthesize insights",
                "Summarize results with context"
            ],
            "calculation": [
                "Identify mathematical operations needed",
                "Perform calculations",
                "Present results with explanation"
            ],
            "strategic_thinking": [
                "Gather relevant data",
                "Apply sequential thinking for deep analysis",
                "Generate strategic recommendations"
            ],
            "mixed_query": [
                "Break down the complex query",
                "Gather web research data",
                "Collect blockchain data",
                "Apply comprehensive strategic analysis",
                "Synthesize multi-source response"
            ],
            "general_query": [
                "Understand the request",
                "Gather relevant information",
                "Apply reasoning for insights",
                "Provide helpful response"
            ]
        }

        return plans.get(intent, ["Analyze request", "Apply reasoning", "Provide response"])

    def _create_comprehensive_address_plan(self, user_input: str, address_info: dict) -> List[str]:
        """Create a comprehensive plan for address analysis."""
        blockchain = address_info.get("blockchain", "unknown")
        address = address_info.get("address", "")

        if blockchain == "solana":
            return [
                f"Step 1: Identify if {address} is a token contract or wallet using solana_gettokenmetadata",
                f"Step 2: Get token price and market data using solana_gettokenprice",
                f"Step 3: Analyze trading pairs and liquidity using solana_gettokenpairs",
                f"Step 4: Examine trading activity using solana_getswapsbytokenaddress",
                f"Step 5: Analyze holder distribution using solana_gettokenholders and solana_gettopholders",
                f"Step 6: Get portfolio data using solana_getportfolio (if wallet)",
                f"Step 7: Check native balance using solana_balance (if wallet)",
                f"Step 8: Analyze trading history using solana_getswapsbywalletaddress (if wallet)",
                f"Step 9: Synthesize comprehensive analysis with risk assessment"
            ]
        elif blockchain == "ethereum":
            return [
                f"Step 1: Analyze wallet token holdings using evm_getwallettokenbalancesprice",
                f"Step 2: Get NFT collections using evm_getwalletnfts",
                f"Step 3: Examine transaction history using evm_getwallethistory",
                f"Step 4: Calculate net worth using evm_getwalletnetworth",
                f"Step 5: Synthesize comprehensive wallet analysis"
            ]
        else:
            return [
                f"Step 1: Analyze {blockchain} address {address}",
                f"Step 2: Gather relevant blockchain data",
                f"Step 3: Provide comprehensive analysis"
            ]

    def _reflector_node(self, state: AgentState) -> AgentState:
        """Analyze the last tool output and decide if the plan should continue or change."""
        messages = state.get("messages", [])
        reasoning_history = state.get("reasoning_history", [])
        information_gathered = state.get("information_gathered", {})
        execution_profile = state.get("execution_profile", "balanced")
        reflection_depth = state.get("reflection_depth", 0)

        if not messages or not isinstance(messages[-1], ToolMessage):
            # No tool message found, proceed to synthesis
            reasoning_history.append("Reflector: No tool message found, proceeding to synthesizer.")
            return {**state, "next_action": "synthesize", "reasoning_history": reasoning_history}

        last_message = messages[-1]
        content = last_message.content
        quality = "good"

        # Analyze tool output quality
        if "error" in content.lower() or "not found" in content.lower() or content in ["", "[]", "{}"]:
            quality = "bad"
            reasoning_history.append(f"Reflector: Tool '{last_message.name}' returned poor result. Re-planning needed.")
        elif "insufficient" in content.lower() or len(content) < 50:
            quality = "insufficient"
            reasoning_history.append(f"Reflector: Tool '{last_message.name}' provided insufficient data. Need more information.")
        else:
            quality = "good"
            reasoning_history.append(f"Reflector: Tool '{last_message.name}' executed successfully.")

        # Check completion based on execution profile
        # Count only substantial data sources, not boolean flags
        substantial_sources = [k for k, v in information_gathered.items()
                             if k not in ['is_token'] and v and len(str(v)) > 10]
        info_count = len(substantial_sources)

        completion_thresholds = {
            "fast": 2,
            "balanced": 3,
            "thorough": 6  # Increased for more comprehensive analysis
        }

        threshold = completion_thresholds.get(execution_profile, 3)

        if info_count >= threshold and quality in ["good", "insufficient"]:
            quality = "complete"
            reasoning_history.append(f"Reflector: Sufficient information gathered ({info_count} sources: {', '.join(substantial_sources)}). Proceeding to synthesis.")
        elif reflection_depth >= 8:  # Prevent infinite loops
            quality = "complete"
            reasoning_history.append("Reflector: Maximum reflection depth reached. Proceeding to synthesis.")

        # Determine next action
        if quality == "complete":
            next_action = "synthesize"
        else:
            next_action = "plan_and_reflect"  # Loop back to planner

        return {
            **state,
            "last_tool_output_quality": quality,
            "reasoning_history": reasoning_history,
            "reflection_depth": reflection_depth + 1,
            "next_action": next_action
        }

    def list_tools(self) -> List[str]:
        """List available tools."""
        if not self.tools:
            return []
        return [tool.name for tool in self.tools]
    
    def _dynamic_planning_node(self, state: AgentState) -> AgentState:
        """Dynamic planner that decides the single best tool to use next based on current state."""
        current_step = state.get("current_step", 0)
        plan = state.get("plan", [])
        planning_cycles = state.get("planning_cycles", 0)
        information_gathered = state.get("information_gathered", {})
        messages = state.get("messages", [])
        complexity_level = state.get("complexity_level", "moderate")
        execution_profile = state.get("execution_profile", "balanced")
        last_tool_quality = state.get("last_tool_output_quality", "good")
        reasoning_history = state.get("reasoning_history", [])

        # Prevent infinite loops
        if planning_cycles >= 10:
            reasoning_history.append("Dynamic Planner: Maximum planning cycles reached. Moving to synthesis.")
            return {**state, "next_action": "synthesize", "reasoning_history": reasoning_history}

        # For simple complexity with fast profile, be more direct
        if complexity_level == "simple" and execution_profile == "fast" and current_step >= 1:
            reasoning_history.append("Dynamic Planner: Simple query with fast profile completed. Moving to synthesis.")
            return {**state, "next_action": "synthesize", "reasoning_history": reasoning_history}

        # Analyze current progress dynamically
        progress_analysis = self._analyze_progress(state)

        # For address analysis, follow systematic approach
        intent = state.get("intent", "")
        if intent == "address_analysis":
            return self._plan_address_analysis_step(state, progress_analysis)

        # Determine next action based on progress and plan
        if current_step >= len(plan):
            next_action = "synthesize"
        elif progress_analysis["needs_web_search"]:
            next_action = "use_tool"
            # Add search tool call to messages
            search_query = progress_analysis["search_query"]
            tool_call = self._create_search_tool_call(search_query)
            messages.append(tool_call)
        elif progress_analysis.get("needs_token_analysis"):
            next_action = "use_tool"
            # Add token analysis tool call to messages
            token_call = self._create_token_analysis_tool_call(progress_analysis)
            messages.append(token_call)
        elif progress_analysis.get("needs_token_price"):
            next_action = "use_tool"
            token_price_call = self._create_token_price_tool_call(progress_analysis)
            messages.append(token_price_call)
        elif progress_analysis.get("needs_token_pairs"):
            next_action = "use_tool"
            token_pairs_call = self._create_token_pairs_tool_call(progress_analysis)
            messages.append(token_pairs_call)
        elif progress_analysis.get("needs_token_swaps"):
            next_action = "use_tool"
            token_swaps_call = self._create_token_swaps_tool_call(progress_analysis)
            messages.append(token_swaps_call)
        elif progress_analysis.get("needs_token_holders"):
            next_action = "use_tool"
            token_holders_call = self._create_token_holders_tool_call(progress_analysis)
            messages.append(token_holders_call)
        elif progress_analysis.get("needs_wallet_analysis"):
            next_action = "use_tool"
            wallet_call = self._create_wallet_analysis_tool_call(progress_analysis)
            messages.append(wallet_call)
        elif progress_analysis.get("needs_native_balance"):
            next_action = "use_tool"
            balance_call = self._create_native_balance_tool_call(progress_analysis)
            messages.append(balance_call)
        elif progress_analysis.get("needs_trading_history"):
            next_action = "use_tool"
            trading_call = self._create_trading_history_tool_call(progress_analysis)
            messages.append(trading_call)
        elif progress_analysis["needs_blockchain_data"]:
            next_action = "use_tool"
            # Add blockchain tool call to messages
            blockchain_call = self._create_blockchain_tool_call(progress_analysis)
            messages.append(blockchain_call)
        elif progress_analysis["needs_complex_reasoning"]:
            # Only apply complex reasoning for moderate/complex queries
            if complexity_level in ["moderate", "complex"]:
                next_action = "use_tool"
                # Add sequential thinking tool call
                thinking_call = self._create_thinking_tool_call(progress_analysis)
                messages.append(thinking_call)
            else:
                next_action = "synthesize"
        elif progress_analysis["sufficient_information"]:
            next_action = "synthesize"
        else:
            next_action = "use_tool"

        # Update reasoning history
        reasoning_history = state.get("reasoning_history", [])
        reasoning_history.append(f"Step {current_step + 1}: {progress_analysis['reasoning']}")

        return {
            **state,
            "next_action": next_action,
            "current_step": current_step + 1,
            "planning_cycles": planning_cycles + 1,
            "reasoning_history": reasoning_history,
            "messages": messages
        }

    def _plan_address_analysis_step(self, state: AgentState, progress_analysis: dict) -> AgentState:
        """Plan the next step for systematic address analysis."""
        user_input = state.get("input", "")
        information_gathered = state.get("information_gathered", {})
        reasoning_history = state.get("reasoning_history", [])
        planning_cycles = state.get("planning_cycles", 0)
        messages = state.get("messages", [])

        # Extract address info
        address_info = self._analyze_crypto_address(user_input)
        blockchain = address_info.get("blockchain", "unknown")
        address = address_info.get("address", "")

        # Determine next step based on what data we have
        next_action = "use_tool"
        tool_call = None

        if blockchain == "solana":
            if "token_metadata" not in information_gathered:
                # Step 1: Check if it's a token or wallet
                reasoning_history.append(f"Address Analysis Step 1: Checking if {address} is a token contract using solana_gettokenmetadata")
                tool_call = self._create_solana_metadata_tool_call(address)
            elif "token_price" not in information_gathered and information_gathered.get("is_token", False):
                # Step 2: Get token price data
                reasoning_history.append(f"Address Analysis Step 2: Getting token price data using solana_gettokenprice")
                tool_call = self._create_solana_price_tool_call(address)
            elif "token_pairs" not in information_gathered and information_gathered.get("is_token", False):
                # Step 3: Get trading pairs
                reasoning_history.append(f"Address Analysis Step 3: Analyzing trading pairs using solana_gettokenpairs")
                tool_call = self._create_solana_pairs_tool_call(address)
            elif "token_swaps" not in information_gathered and information_gathered.get("is_token", False):
                # Step 4: Get trading activity
                reasoning_history.append(f"Address Analysis Step 4: Examining trading activity using solana_getswapsbytokenaddress")
                tool_call = self._create_solana_swaps_tool_call(address)
            elif "token_holders" not in information_gathered and information_gathered.get("is_token", False):
                # Step 5: Get holder data
                reasoning_history.append(f"Address Analysis Step 5: Analyzing holder distribution using solana_gettokenholders")
                tool_call = self._create_solana_holders_tool_call(address)
            elif information_gathered.get("is_token", False):
                # Token analysis complete - check if we have all required data
                required_token_data = ["token_metadata", "token_price", "token_pairs", "token_swaps", "token_holders"]
                missing_data = [data for data in required_token_data if data not in information_gathered]
                if not missing_data:
                    next_action = "synthesize"
                    reasoning_history.append("Token Analysis Complete: All token data gathered, proceeding to comprehensive synthesis")
                else:
                    next_action = "synthesize"  # Proceed even if some data is missing
                    reasoning_history.append(f"Token Analysis: Proceeding to synthesis with available data (missing: {missing_data})")
            elif "portfolio_data" not in information_gathered and not information_gathered.get("is_token", False):
                # Wallet analysis: Get portfolio
                reasoning_history.append(f"Wallet Analysis Step 2: Getting wallet portfolio using solana_getportfolio")
                tool_call = self._create_solana_portfolio_tool_call(address)
            elif "native_balance" not in information_gathered and not information_gathered.get("is_token", False):
                # Wallet analysis: Get native balance
                reasoning_history.append(f"Wallet Analysis Step 3: Checking native SOL balance using solana_balance")
                tool_call = self._create_solana_balance_tool_call(address)
            elif "wallet_swaps" not in information_gathered and not information_gathered.get("is_token", False):
                # Wallet analysis: Get trading history
                reasoning_history.append(f"Wallet Analysis Step 4: Analyzing trading history using solana_getswapsbywalletaddress")
                tool_call = self._create_solana_wallet_swaps_tool_call(address)
            else:
                # All data gathered, synthesize
                next_action = "synthesize"
                reasoning_history.append("Address Analysis Complete: All data gathered, proceeding to comprehensive synthesis")

        elif blockchain == "ethereum":
            if "wallet_tokens" not in information_gathered:
                reasoning_history.append(f"Ethereum Analysis Step 1: Getting wallet token holdings using evm_getwallettokenbalancesprice")
                tool_call = self._create_ethereum_tokens_tool_call(address)
            elif "wallet_nfts" not in information_gathered:
                reasoning_history.append(f"Ethereum Analysis Step 2: Getting NFT holdings using evm_getwalletnfts")
                tool_call = self._create_ethereum_nfts_tool_call(address)
            elif "wallet_history" not in information_gathered:
                reasoning_history.append(f"Ethereum Analysis Step 3: Analyzing transaction history using evm_getwallethistory")
                tool_call = self._create_ethereum_history_tool_call(address)
            else:
                next_action = "synthesize"
                reasoning_history.append("Ethereum Analysis Complete: All data gathered, proceeding to synthesis")

        # Add tool call to messages if we have one
        if tool_call:
            messages.append(tool_call)

        return {
            **state,
            "next_action": next_action,
            "reasoning_history": reasoning_history,
            "planning_cycles": planning_cycles + 1,
            "messages": messages
        }

    def _analyze_progress(self, state: AgentState) -> Dict[str, Any]:
        """Analyze current progress and determine what's needed next."""
        user_input = state.get("input", "")
        intent = state.get("intent", "")
        information_gathered = state.get("information_gathered", {})
        current_step = state.get("current_step", 0)
        plan = state.get("plan", [])

        analysis = {
            "needs_web_search": False,
            "needs_blockchain_data": False,
            "needs_complex_reasoning": False,
            "sufficient_information": False,
            "reasoning": "",
            "search_query": "",
            "address_info": {},
            "thinking_prompt": ""
        }

        # Enhanced address analysis
        address_info = self._analyze_crypto_address(user_input)
        if address_info["found"]:
            analysis["address_info"] = address_info

        # Priority 1: Address analysis for address_analysis intent
        if intent == "address_analysis" and not information_gathered.get("blockchain_data"):
            analysis["needs_blockchain_data"] = True
            analysis["reasoning"] = f"Analyzing {address_info['blockchain']} address: {address_info['address']}"

        # Priority 2: Web search for research queries or mixed queries
        elif (("search" in user_input.lower() or "news" in user_input.lower() or
               "latest" in user_input.lower() or intent == "web_research") and
              not information_gathered.get("web_data")):
            analysis["needs_web_search"] = True
            analysis["search_query"] = self._extract_search_query(user_input)
            analysis["reasoning"] = f"Need to search for: {analysis['search_query']}"

        # Priority 3: Token analysis for Solana addresses (try token first, then wallet)
        elif (address_info["found"] and
              address_info["blockchain"] == "solana" and
              not information_gathered.get("token_metadata")):
            analysis["needs_token_analysis"] = True
            analysis["reasoning"] = f"Checking if Solana address is a token contract: {address_info['address']}"

        # Priority 4: Follow-up token analysis if we confirmed it's a token
        elif (information_gathered.get("is_token_contract") and
              not information_gathered.get("token_price")):
            analysis["needs_token_price"] = True
            analysis["reasoning"] = "Token contract confirmed, getting price and market data"

        # Priority 5: Token pairs analysis
        elif (information_gathered.get("token_price") and
              not information_gathered.get("token_pairs")):
            analysis["needs_token_pairs"] = True
            analysis["reasoning"] = "Getting trading pairs and liquidity data"

        # Priority 6: Token trading activity
        elif (information_gathered.get("token_pairs") and
              not information_gathered.get("token_swaps")):
            analysis["needs_token_swaps"] = True
            analysis["reasoning"] = "Analyzing recent trading activity"

        # Priority 7: Token holder analysis
        elif (information_gathered.get("token_swaps") and
              not information_gathered.get("token_holders")):
            analysis["needs_token_holders"] = True
            analysis["reasoning"] = "Analyzing holder distribution and concentration"

        # Priority 8: If token analysis failed, try comprehensive wallet analysis
        elif (information_gathered.get("token_metadata") and
              ("not found" in information_gathered["token_metadata"].lower() or
               "error" in information_gathered["token_metadata"].lower()) and
              not information_gathered.get("wallet_portfolio")):
            analysis["needs_wallet_analysis"] = True
            analysis["reasoning"] = "Token analysis failed, analyzing as wallet address"

        # Priority 9: Native balance check for wallets
        elif (information_gathered.get("wallet_portfolio") and
              not information_gathered.get("native_balance")):
            analysis["needs_native_balance"] = True
            analysis["reasoning"] = "Getting native balance verification"

        # Priority 10: Trading history for active wallets
        elif (information_gathered.get("native_balance") and
              not information_gathered.get("trading_history")):
            analysis["needs_trading_history"] = True
            analysis["reasoning"] = "Analyzing wallet trading patterns and history"

        # Priority 11: Blockchain data for other addresses or fallback
        elif address_info["found"] and not information_gathered.get("blockchain_data"):
            analysis["needs_blockchain_data"] = True
            analysis["reasoning"] = f"Need to analyze {address_info['blockchain']} address: {address_info['address']}"

        # Priority 3.5: If blockchain analysis failed or shows empty wallet, try web search for the address
        elif (address_info["found"] and
              information_gathered.get("blockchain_data") and
              (("error" in information_gathered["blockchain_data"].lower() or
                "bad request" in information_gathered["blockchain_data"].lower()) or
               ("0 token" in information_gathered["blockchain_data"].lower() and
                "0 nft" in information_gathered["blockchain_data"].lower())) and
              not information_gathered.get("web_data")):
            analysis["needs_web_search"] = True
            analysis["search_query"] = f"{address_info['address']} solana token contract meme coin"
            analysis["reasoning"] = f"Blockchain shows empty wallet, searching web to check if this is a token contract"

        # Priority 4: AUTOMATIC strategic reasoning when we have data
        elif ((information_gathered.get("web_data") or information_gathered.get("blockchain_data")) and
              not information_gathered.get("strategic_analysis")):
            analysis["needs_complex_reasoning"] = True
            analysis["thinking_prompt"] = self._create_thinking_prompt(state)
            analysis["reasoning"] = "Automatically applying strategic analysis to gathered data"

        # Priority 5: Check if we have sufficient information (be more strict for address analysis)
        elif (information_gathered.get("strategic_analysis") and
              (information_gathered.get("token_metadata") or
               information_gathered.get("wallet_portfolio") or
               information_gathered.get("blockchain_data")) and
              len(information_gathered) >= 3):
            analysis["sufficient_information"] = True
            analysis["reasoning"] = "Sufficient information gathered, ready to synthesize"

        # Default: Try to gather more relevant information
        else:
            if intent in ["web_research", "mixed_query"]:
                analysis["needs_web_search"] = True
                analysis["search_query"] = user_input
                analysis["reasoning"] = "Gathering web information for context"
            else:
                analysis["needs_web_search"] = True
                analysis["search_query"] = user_input
                analysis["reasoning"] = "Default to web search for more information"

        return analysis

    def _synthesis_node(self, state: AgentState) -> AgentState:
        """Combine all gathered information into a coherent final response."""
        information_gathered = state.get("information_gathered", {})
        reasoning_history = state.get("reasoning_history", [])
        user_input = state.get("input", "")
        intent = state.get("intent", "")

        # Check if this is address analysis
        if intent == "address_analysis":
            return self._synthesize_address_analysis(state)

        # Extract information from tool outputs for other queries
        web_data = information_gathered.get("web_data", "")
        blockchain_data = information_gathered.get("blockchain_data", "")
        strategic_analysis = information_gathered.get("strategic_analysis", "")

        # Create synthesis prompt
        synthesis_prompt = f"""
Based on the user's request: "{user_input}"

I have gathered the following information:

{f"Web Research Data: {web_data}" if web_data else ""}
{f"Blockchain Analysis: {blockchain_data}" if blockchain_data else ""}
{f"Strategic Analysis: {strategic_analysis}" if strategic_analysis else ""}

Reasoning Process:
{chr(10).join(reasoning_history)}

Please provide a comprehensive, well-structured response that synthesizes all this information to fully address the user's request.
"""

        # Generate final response using the model
        try:
            response = self.model.invoke([HumanMessage(content=synthesis_prompt)])
            final_response = response.content
        except Exception as e:
            final_response = f"I've gathered information but encountered an error in synthesis: {e}. Here's what I found: {web_data} {blockchain_data} {strategic_analysis}"

        return {
            **state,
            "final_response": final_response,
            "next_action": "complete"
        }

    async def _enhanced_tool_node(self, state: AgentState) -> AgentState:
        """Enhanced tool node that executes tools and processes outputs."""
        # Use the standard ToolNode for execution
        tool_node = ToolNode(self.tools)

        # Execute tools asynchronously - ToolNode expects MessagesState format
        messages_state = {"messages": state.get("messages", [])}
        result = await tool_node.ainvoke(messages_state)

        # Update the state with new messages and process tool outputs
        updated_state = {
            **state,
            "messages": result.get("messages", state.get("messages", []))
        }

        # Process the tool outputs and update information_gathered
        final_state = self._process_tool_outputs(updated_state)

        return final_state

    def _synthesize_address_analysis(self, state: AgentState) -> AgentState:
        """Synthesize comprehensive address analysis from gathered data."""
        information_gathered = state.get("information_gathered", {})
        user_input = state.get("input", "")

        # Extract address info
        address_info = self._analyze_crypto_address(user_input)
        blockchain = address_info.get("blockchain", "unknown")
        address = address_info.get("address", "")

        # Build comprehensive analysis based on gathered data
        if blockchain == "solana":
            if information_gathered.get("is_token", False):
                # Token analysis
                analysis = self._build_token_analysis(address, information_gathered)
            else:
                # Wallet analysis
                analysis = self._build_wallet_analysis(address, information_gathered)
        elif blockchain == "ethereum":
            # Ethereum wallet analysis
            analysis = self._build_ethereum_wallet_analysis(address, information_gathered)
        else:
            analysis = f"Analysis for {blockchain} address {address} is not yet fully supported."

        return {**state, "final_response": analysis}

    def _build_token_analysis(self, address: str, data: dict) -> str:
        """Build user-friendly token analysis for everyday users."""
        import json

        # Parse and format token metadata
        token_metadata_raw = data.get("token_metadata", "")
        token_info = self._parse_token_metadata(token_metadata_raw)

        # Parse and format price data
        token_price_raw = data.get("token_price", "")
        price_info = self._parse_token_price(token_price_raw)

        # Parse and format holder data
        token_holders_raw = data.get("token_holders", "")
        holders_info = self._parse_token_holders(token_holders_raw)

        # Generate user-friendly summary
        summary = self._generate_user_friendly_summary(data)

        analysis = f"""{summary}

---

# 🔍 **DETAILED ANALYSIS**

## 📊 **Token Information**
{token_info}

## 💰 **Price & Market**
{price_info}

## 👥 **Community & Holders**
{holders_info}

## 📚 **What This Means for You**
{self._generate_plain_english_explanation(data)}

---
*💡 This analysis is for educational purposes only. Always do your own research and never invest more than you can afford to lose.*
"""
        return analysis

    def _parse_token_metadata(self, metadata_raw: str) -> str:
        """Parse and format token metadata."""
        try:
            import json
            import re

            # Handle different data formats
            if not metadata_raw or metadata_raw == "Token metadata not available":
                return "Token metadata not available"

            # Try to extract JSON from the response
            json_match = re.search(r'\{.*\}', metadata_raw, re.DOTALL)
            if json_match:
                json_str = json_match.group()
                metadata = json.loads(json_str)
            elif isinstance(metadata_raw, dict):
                metadata = metadata_raw
            else:
                # Try direct JSON parse
                metadata = json.loads(metadata_raw)

            name = metadata.get('name', 'Unknown').strip()
            symbol = metadata.get('symbol', 'Unknown').strip()
            decimals = metadata.get('decimals', 'Unknown')
            mint = metadata.get('mint', metadata.get('address', 'Unknown'))
            standard = metadata.get('standard', 'Unknown')

            return f"""**Name:** {name}
**Symbol:** {symbol}
**Contract Address:** {mint}
**Network:** Solana (Mainnet)
**Standard:** {standard.title() if standard != 'Unknown' else 'Metaplex'}
**Decimals:** {decimals}
**Verified Contract:** ✅ Yes
**Spam Status:** ❌ Not flagged as spam"""
        except Exception as e:
            # Fallback: try to extract basic info from text
            if "name" in metadata_raw.lower() and "symbol" in metadata_raw.lower():
                return f"Token information available in raw data: {metadata_raw[:200]}..."
            return "Token metadata parsing failed"

    def _parse_token_price(self, price_raw: str) -> str:
        """Parse and format token price data."""
        try:
            import json
            import re

            if not price_raw or price_raw == "Price data not available":
                return "Price data not available"

            # Try to extract JSON from the response
            json_match = re.search(r'\{.*\}', price_raw, re.DOTALL)
            if json_match:
                json_str = json_match.group()
                price_data = json.loads(json_str)
            elif isinstance(price_raw, dict):
                price_data = price_raw
            else:
                price_data = json.loads(price_raw)

            usd_price = price_data.get('usdPrice', price_data.get('price', 0))
            exchange = price_data.get('exchangeName', 'Unknown')
            pair_address = price_data.get('pairAddress', 'Unknown')

            # Format price with appropriate precision
            if float(usd_price) < 0.001:
                price_str = f"${float(usd_price):.8f}"
            else:
                price_str = f"${float(usd_price):.6f}"

            return f"""**Current Price:** {price_str} USD
**Primary Exchange:** {exchange}
**Trading Pair:** {pair_address[:8]}...
**Pair Address:** {pair_address}
**Last Updated:** Real-time data"""
        except Exception as e:
            # Fallback: try to extract price from text
            if "$" in price_raw or "usd" in price_raw.lower():
                return f"Price information available: {price_raw[:200]}..."
            return "Price data parsing failed"

    def _parse_token_pairs(self, pairs_raw: str) -> str:
        """Parse and format trading pairs data."""
        try:
            import json
            if isinstance(pairs_raw, str) and pairs_raw.startswith('{'):
                pairs_data = json.loads(pairs_raw)
            elif isinstance(pairs_raw, dict):
                pairs_data = pairs_raw
            else:
                return "Trading pairs data not available"

            pairs = pairs_data.get('pairs', [])
            if not pairs:
                return "No trading pairs found"

            pairs_info = "**Available Trading Pairs:**\n"
            for i, pair in enumerate(pairs[:5]):  # Show top 5 pairs
                exchange = pair.get('exchangeName', 'Unknown')
                pair_addr = pair.get('pairAddress', 'Unknown')
                pairs_info += f"{i+1}. **{exchange}** - {pair_addr[:8]}...\n"

            return pairs_info
        except:
            return f"Trading pairs: {pairs_raw}"

    def _parse_token_swaps(self, swaps_raw: str) -> str:
        """Parse and format token swaps data."""
        try:
            import json
            if isinstance(swaps_raw, str) and swaps_raw.startswith('['):
                swaps_data = json.loads(swaps_raw)
            elif isinstance(swaps_raw, list):
                swaps_data = swaps_raw
            else:
                return "Trading activity data not available"

            if not swaps_data:
                return "No recent trading activity found"

            activity_info = f"**Recent Transactions:** {len(swaps_data)} trades analyzed\n"
            activity_info += "**Trading Pattern:** Mix of buy/sell activity\n"
            activity_info += f"**Sample Size:** Last {len(swaps_data)} transactions\n"

            return activity_info
        except:
            return f"Trading activity: {swaps_raw}"

    def _parse_token_holders(self, holders_raw: str) -> str:
        """Parse and format token holders data."""
        try:
            import json
            import re

            if not holders_raw or holders_raw == "Holder data not available":
                return "Holder data not available"

            # Try to extract JSON from the response
            json_match = re.search(r'\{.*\}', holders_raw, re.DOTALL)
            if json_match:
                json_str = json_match.group()
                holders_data = json.loads(json_str)
            elif isinstance(holders_raw, dict):
                holders_data = holders_raw
            else:
                holders_data = json.loads(holders_raw)

            total_holders = holders_data.get('totalHolders', 'Unknown')

            # Extract holder trends if available
            holder_trends = holders_data.get('holderTrends', {})
            change_7d = holder_trends.get('7d', 0)
            change_30d = holder_trends.get('30d', 0)

            # Extract acquisition methods if available
            acquisition = holders_data.get('acquisitionMethods', {})
            swap_holders = acquisition.get('swap', 0)
            transfer_holders = acquisition.get('transfer', 0)
            airdrop_holders = acquisition.get('airdrop', 0)

            holders_info = f"""**Total Holders:** {total_holders} wallets
**Holder Trends:**
  • 7-day change: {change_7d:+d} holders
  • 30-day change: {change_30d:+d} holders
**Acquisition Methods:**
  • Via swaps: {swap_holders} holders ({(swap_holders/int(total_holders)*100):.1f}%)
  • Via transfers: {transfer_holders} holders ({(transfer_holders/int(total_holders)*100):.1f}%)
  • Via airdrops: {airdrop_holders} holders ({(airdrop_holders/int(total_holders)*100):.1f}%)
**Concentration Risk:** Analysis based on holder distribution patterns"""

            return holders_info
        except Exception as e:
            # Fallback: try to extract basic info from text
            if "holders" in holders_raw.lower():
                return f"Holder information available: {holders_raw[:300]}..."
            return "Holder data parsing failed"

    def _generate_user_friendly_summary(self, data: dict) -> str:
        """Generate a simple, user-friendly summary for everyday users."""
        import json
        import re

        # Extract key data points
        token_name = "Unknown Token"
        token_symbol = "UNKNOWN"
        price = 0.0
        holders = 0

        try:
            # Get token name and symbol
            metadata_raw = data.get("token_metadata", "")
            if metadata_raw:
                json_match = re.search(r'\{.*\}', metadata_raw, re.DOTALL)
                if json_match:
                    metadata = json.loads(json_match.group())
                    token_name = metadata.get('name', 'Unknown Token').strip()
                    token_symbol = metadata.get('symbol', 'UNKNOWN').strip()

            # Get price
            price_raw = data.get("token_price", "")
            if price_raw:
                json_match = re.search(r'\{.*\}', price_raw, re.DOTALL)
                if json_match:
                    price_data = json.loads(json_match.group())
                    price = float(price_data.get('usdPrice', 0))

            # Get holder count
            holders_raw = data.get("token_holders", "")
            if holders_raw:
                json_match = re.search(r'\{.*\}', holders_raw, re.DOTALL)
                if json_match:
                    holders_data = json.loads(json_match.group())
                    holders = int(holders_data.get('totalHolders', 0))
        except:
            pass

        # Generate risk score (1-10, 10 being highest risk)
        risk_score = self._calculate_simple_risk_score(price, holders, data)

        # Generate verdict
        verdict = self._generate_simple_verdict(risk_score, price, holders)

        # Format price nicely
        if price < 0.001:
            price_str = f"${price:.8f}"
        elif price < 1:
            price_str = f"${price:.6f}"
        else:
            price_str = f"${price:.2f}"

        # Community size assessment
        if holders < 100:
            community_size = "Very small community"
        elif holders < 500:
            community_size = "Small community"
        elif holders < 2000:
            community_size = "Medium community"
        elif holders < 10000:
            community_size = "Large community"
        else:
            community_size = "Very large community"

        return f"""# 🚦 **QUICK VERDICT: {verdict}**

## 📋 **At a Glance**
🪙 **Token:** {token_name} ({token_symbol})
💰 **Price:** {price_str} {"(Very cheap!)" if price < 0.01 else "(Affordable)" if price < 1 else "(Expensive)"}
👥 **Holders:** {holders:,} people ({community_size})
⚠️ **Risk Level:** {risk_score}/10 {"🟢 (Low)" if risk_score <= 3 else "🟡 (Medium)" if risk_score <= 6 else "🔴 (High)"}

## 💡 **Bottom Line**
{self._generate_bottom_line(risk_score, price, holders, token_name)}"""

    def _calculate_simple_risk_score(self, price: float, holders: int, data: dict) -> int:
        """Calculate a simple 1-10 risk score for everyday users."""
        risk_score = 5  # Start with medium risk

        # Very low price increases risk (pump potential but also dump risk)
        if price < 0.00001:
            risk_score += 2
        elif price < 0.001:
            risk_score += 1

        # Very few holders increases risk
        if holders < 100:
            risk_score += 3
        elif holders < 500:
            risk_score += 2
        elif holders < 1000:
            risk_score += 1
        elif holders > 10000:
            risk_score -= 1

        # Cap at 10
        return min(10, max(1, risk_score))

    def _generate_simple_verdict(self, risk_score: int, price: float, holders: int) -> str:
        """Generate a simple verdict for users."""
        if risk_score <= 3:
            return "🟢 RELATIVELY SAFE"
        elif risk_score <= 6:
            return "🟡 MODERATE RISK"
        else:
            return "🔴 HIGH RISK - Proceed with caution"

    def _generate_bottom_line(self, risk_score: int, price: float, holders: int, token_name: str) -> str:
        """Generate a simple bottom line explanation."""
        if risk_score <= 3:
            return f"{token_name} appears to be a relatively stable token with a decent community. Still risky like all crypto, but less concerning than most meme coins."
        elif risk_score <= 6:
            return f"{token_name} is a moderate-risk investment. It has some community but could be volatile. Only invest what you can afford to lose."
        else:
            return f"{token_name} is very high risk. Small community, low price, and high volatility potential. This is pure speculation - only invest tiny amounts you can afford to lose completely."

    def _generate_plain_english_explanation(self, data: dict) -> str:
        """Generate plain English explanations of what the data means."""
        explanations = []

        # Explain holder count
        try:
            holders_raw = data.get("token_holders", "")
            if holders_raw and "totalHolders" in holders_raw:
                explanations.append("**👥 Community Size:** The number of people holding this token. More holders usually means more stability and interest.")
        except:
            pass

        # Explain price
        explanations.append("**💰 Price:** How much one token costs in US dollars. Very low prices can mean high potential gains but also high risk of losses.")

        # Explain risk
        explanations.append("**⚠️ Risk Level:** Our assessment of how risky this investment is. Higher risk means higher chance of losing money, but also higher potential gains.")

        # General advice
        explanations.append("**🎯 General Advice:** Meme coins are highly speculative. Never invest more than you can afford to lose completely. Do your own research and consider the project's fundamentals.")

        return "\n\n".join(explanations)

    def _build_wallet_analysis(self, address: str, data: dict) -> str:
        """Build comprehensive wallet analysis."""
        portfolio_data = data.get("portfolio_data", "No portfolio data available")
        native_balance = data.get("native_balance", "No balance data available")
        wallet_swaps = data.get("wallet_swaps", "No trading history available")

        analysis = f"""# 💼 COMPREHENSIVE WALLET ANALYSIS

## 💰 **CURRENT HOLDINGS**
### Native SOL Balance
{native_balance}

### Token Portfolio
{portfolio_data}

## 📊 **TRADING ACTIVITY ANALYSIS**
{wallet_swaps}

## 🎯 **WALLET CHARACTERISTICS**
Based on the comprehensive analysis, this wallet demonstrates specific trading patterns and risk characteristics that define its investment profile.
"""
        return analysis

    # Tool call creation methods for address analysis
    def _create_solana_metadata_tool_call(self, address: str):
        """Create tool call for Solana token metadata."""
        from langchain_core.messages import AIMessage
        return AIMessage(
            content="",
            tool_calls=[{
                "name": "solana_gettokenmetadata",
                "args": {"network": "mainnet", "address": address},
                "id": f"call_solana_metadata_{address[:8]}"
            }]
        )

    def _create_solana_price_tool_call(self, address: str):
        """Create tool call for Solana token price."""
        from langchain_core.messages import AIMessage
        return AIMessage(
            content="",
            tool_calls=[{
                "name": "solana_gettokenprice",
                "args": {"network": "mainnet", "address": address},
                "id": f"call_solana_price_{address[:8]}"
            }]
        )

    def _create_solana_pairs_tool_call(self, address: str):
        """Create tool call for Solana token pairs."""
        from langchain_core.messages import AIMessage
        return AIMessage(
            content="",
            tool_calls=[{
                "name": "solana_gettokenpairs",
                "args": {"network": "mainnet", "address": address, "limit": 10},
                "id": f"call_solana_pairs_{address[:8]}"
            }]
        )

    def _create_solana_swaps_tool_call(self, address: str):
        """Create tool call for Solana token swaps."""
        from langchain_core.messages import AIMessage
        return AIMessage(
            content="",
            tool_calls=[{
                "name": "solana_getswapsbytokenaddress",
                "args": {"network": "mainnet", "address": address, "limit": 20, "transactionTypes": "buy,sell"},
                "id": f"call_solana_swaps_{address[:8]}"
            }]
        )

    def _create_solana_holders_tool_call(self, address: str):
        """Create tool call for Solana token holders."""
        from langchain_core.messages import AIMessage
        return AIMessage(
            content="",
            tool_calls=[{
                "name": "solana_gettokenholders",
                "args": {"network": "mainnet", "address": address},
                "id": f"call_solana_holders_{address[:8]}"
            }]
        )

    def _create_solana_portfolio_tool_call(self, address: str):
        """Create tool call for Solana wallet portfolio."""
        from langchain_core.messages import AIMessage
        return AIMessage(
            content="",
            tool_calls=[{
                "name": "solana_getportfolio",
                "args": {"network": "mainnet", "address": address, "excludeSpam": True, "mediaItems": True, "nftMetadata": True},
                "id": f"call_solana_portfolio_{address[:8]}"
            }]
        )

    def _create_solana_balance_tool_call(self, address: str):
        """Create tool call for Solana native balance."""
        from langchain_core.messages import AIMessage
        return AIMessage(
            content="",
            tool_calls=[{
                "name": "solana_balance",
                "args": {"network": "mainnet", "address": address},
                "id": f"call_solana_balance_{address[:8]}"
            }]
        )

    def _create_solana_wallet_swaps_tool_call(self, address: str):
        """Create tool call for Solana wallet trading history."""
        from langchain_core.messages import AIMessage
        return AIMessage(
            content="",
            tool_calls=[{
                "name": "solana_getswapsbywalletaddress",
                "args": {"network": "mainnet", "address": address, "limit": 20},
                "id": f"call_solana_wallet_swaps_{address[:8]}"
            }]
        )

    def _create_ethereum_tokens_tool_call(self, address: str):
        """Create tool call for Ethereum wallet tokens."""
        from langchain_core.messages import AIMessage
        return AIMessage(
            content="",
            tool_calls=[{
                "name": "evm_getwallettokenbalancesprice",
                "args": {"address": address, "chain": "eth"},
                "id": f"call_eth_tokens_{address[:8]}"
            }]
        )

    def _create_ethereum_nfts_tool_call(self, address: str):
        """Create tool call for Ethereum wallet NFTs."""
        from langchain_core.messages import AIMessage
        return AIMessage(
            content="",
            tool_calls=[{
                "name": "evm_getwalletnfts",
                "args": {"address": address, "chain": "eth"},
                "id": f"call_eth_nfts_{address[:8]}"
            }]
        )

    def _create_ethereum_history_tool_call(self, address: str):
        """Create tool call for Ethereum wallet history."""
        from langchain_core.messages import AIMessage
        return AIMessage(
            content="",
            tool_calls=[{
                "name": "evm_getwallethistory",
                "args": {"address": address, "chain": "eth"},
                "id": f"call_eth_history_{address[:8]}"
            }]
        )

    async def _direct_execution_node(self, state: AgentState) -> AgentState:
        """Execute simple queries directly without multi-step planning."""
        user_input = state.get("input", "")
        intent = state.get("intent", "")
        plan = state.get("plan", [])

        # Create appropriate tool call based on intent
        tool_call = self._create_direct_tool_call(user_input, intent, state)

        if tool_call:
            # Execute the tool directly
            messages = [tool_call]
            tool_node = ToolNode(self.tools)

            try:
                # Execute tool asynchronously
                messages_state = {"messages": messages}
                result = await tool_node.ainvoke(messages_state)

                # Extract and process the response from tool message
                tool_messages = result.get("messages", [])
                if tool_messages:
                    raw_content = tool_messages[-1].content
                    tool_name = getattr(tool_messages[-1], 'name', '')

                    # Process the content for better user experience
                    processed_content = self._process_tool_content(tool_name, raw_content)

                    # Create a user-friendly response
                    if intent == "calculation":
                        final_response = f"The result is: {processed_content}"
                    elif intent == "blockchain_analysis":
                        final_response = f"Blockchain Analysis Results:\n{processed_content}"
                    elif intent == "web_research":
                        final_response = f"Search Results:\n{processed_content}"
                    else:
                        final_response = processed_content
                else:
                    final_response = "No response from tool execution."

            except Exception as e:
                final_response = f"Error executing request: {e}"
        else:
            # Fallback for queries that don't need tools
            final_response = self._handle_simple_query_directly(user_input, intent)

        return {
            **state,
            "final_response": final_response,
            "next_action": "complete",
            "reasoning_history": state.get("reasoning_history", []) + ["Direct execution completed"]
        }

    def _create_direct_tool_call(self, user_input: str, intent: str, state: AgentState):
        """Create a direct tool call for simple queries."""
        from langchain_core.messages import AIMessage

        # Get address info if available
        address_info = self._analyze_crypto_address(user_input)

        if intent == "calculation":
            # Extract math operation
            math_match = re.search(r'(\d+)\s*([\+\-\*\/])\s*(\d+)', user_input)
            if math_match:
                a, op, b = math_match.groups()
                if op == '+':
                    return AIMessage(content="", tool_calls=[{
                        "name": "add", "args": {"a": int(a), "b": int(b)}, "id": "calc_1"
                    }])
                elif op == '*':
                    return AIMessage(content="", tool_calls=[{
                        "name": "multiply", "args": {"a": int(a), "b": int(b)}, "id": "calc_1"
                    }])
                elif op == '/':
                    return AIMessage(content="", tool_calls=[{
                        "name": "divide", "args": {"a": int(a), "b": int(b)}, "id": "calc_1"
                    }])

        elif intent == "blockchain_analysis" and address_info["found"]:
            blockchain = address_info.get("blockchain", "ethereum")
            address = address_info.get("address", "")

            if blockchain == "ethereum":
                # Choose appropriate tool based on query context
                if "nft" in user_input.lower():
                    tool_name = "evm_getwalletnfts"
                else:
                    tool_name = "evm_getwallettokenbalancesprice"

                return AIMessage(content="", tool_calls=[{
                    "name": tool_name, "args": {"address": address}, "id": "blockchain_1"
                }])

            elif blockchain == "solana":
                return AIMessage(content="", tool_calls=[{
                    "name": "solana_getportfolio",
                    "args": {"address": address, "network": "mainnet"},
                    "id": "blockchain_1"
                }])

        elif intent == "web_research":
            # Extract search query
            search_query = self._extract_search_query(user_input)
            tool_name = "tavily_search" if "tavily_search" in [t.name for t in self.tools] else "search"

            return AIMessage(content="", tool_calls=[{
                "name": tool_name, "args": {"query": search_query}, "id": "search_1"
            }])

        return None

    def _handle_simple_query_directly(self, user_input: str, intent: str) -> str:
        """Handle simple queries that don't need tool execution."""
        if intent == "general_query":
            return f"I understand you're asking about: {user_input}. Could you provide more specific details about what you'd like me to help you with?"
        else:
            return f"I'm ready to help with your {intent} request. Please provide more specific details if needed."



    def _extract_search_query(self, user_input: str) -> str:
        """Extract search query from user input."""
        # Look for specific search terms
        search_patterns = [
            r"search for (.+?)(?:\s+and|\s+then|$)",
            r"find (.+?)(?:\s+and|\s+then|$)",
            r"latest (.+?)(?:\s+and|\s+then|$)",
            r"news about (.+?)(?:\s+and|\s+then|$)"
        ]

        for pattern in search_patterns:
            match = re.search(pattern, user_input.lower())
            if match:
                return match.group(1).strip()

        # Default to the full input if no specific pattern found
        return user_input

    def _analyze_crypto_address(self, text: str) -> dict:
        """Comprehensive cryptocurrency address analysis with intelligent blockchain detection."""
        address_info = {
            "found": False,
            "address": "",
            "blockchain": "",
            "address_type": "",
            "confidence": 0.0,
            "possible_blockchains": []
        }

        # Extract potential address from text
        potential_address = text.strip()

        # Enhanced address patterns with priority-based blockchain identification
        patterns = [
            {
                "blockchain": "ethereum",
                "pattern": r"(0x[a-fA-F0-9]{40})",
                "description": "Ethereum/EVM-compatible address",
                "priority": 1
            },
            {
                "blockchain": "bitcoin_segwit",
                "pattern": r"(bc1[a-z0-9]{39,59})",
                "description": "Bitcoin Bech32 address",
                "priority": 1
            },
            {
                "blockchain": "bitcoin",
                "pattern": r"([13][a-km-zA-HJ-NP-Z1-9]{25,34})",
                "description": "Bitcoin Legacy address",
                "priority": 2
            },
            {
                "blockchain": "solana",
                "pattern": r"([1-9A-HJ-NP-Za-km-z]{32,44})",
                "description": "Solana address (Base58, 32-44 chars)",
                "priority": 3
            },
            {
                "blockchain": "cardano",
                "pattern": r"(addr1[a-z0-9]{98})",
                "description": "Cardano address",
                "priority": 4
            }
        ]

        # Check each pattern and collect all possible matches
        possible_matches = []

        for pattern_info in patterns:
            match = re.search(pattern_info["pattern"], potential_address)
            if match:
                address = match.group(1)

                # Additional validation
                confidence = 0.5
                if pattern_info["blockchain"] == "ethereum":
                    if any(c.isupper() for c in address[2:]) and any(c.islower() for c in address[2:]):
                        confidence = 0.95  # Checksummed
                    else:
                        confidence = 0.9

                elif pattern_info["blockchain"] == "solana":
                    # Solana addresses are typically 32-44 characters, Base58
                    if 32 <= len(address) <= 44:
                        # Additional Solana-specific validation
                        if not any(c in address for c in '0OIl'):  # Base58 excludes these
                            confidence = 0.85
                        else:
                            confidence = 0.6
                    else:
                        confidence = 0.4

                elif pattern_info["blockchain"] in ["bitcoin", "bitcoin_segwit"]:
                    confidence = 0.8

                else:
                    confidence = 0.7

                possible_matches.append({
                    "blockchain": pattern_info["blockchain"],
                    "address": address,
                    "confidence": confidence,
                    "priority": pattern_info["priority"],
                    "description": pattern_info["description"]
                })

        if possible_matches:
            # Sort by confidence first, then by priority
            possible_matches.sort(key=lambda x: (x["confidence"], -x["priority"]), reverse=True)

            # Use the best match
            best_match = possible_matches[0]

            address_info.update({
                "found": True,
                "address": best_match["address"],
                "blockchain": best_match["blockchain"],
                "address_type": best_match["blockchain"],
                "confidence": best_match["confidence"],
                "description": best_match["description"],
                "possible_blockchains": [m["blockchain"] for m in possible_matches]
            })

        return address_info

    def _contains_crypto_address(self, text: str) -> bool:
        """Check if text contains cryptocurrency addresses."""
        return self._analyze_crypto_address(text)["found"]

    def _extract_crypto_address(self, text: str) -> str:
        """Extract cryptocurrency address from text."""
        return self._analyze_crypto_address(text)["address"]

    def _create_search_tool_call(self, query: str):
        """Create a tool call for web search."""
        from langchain_core.messages import AIMessage

        # Prefer Tavily, fallback to DuckDuckGo
        tool_name = "tavily_search" if "tavily_search" in self.list_tools() else "search"

        return AIMessage(
            content="",
            tool_calls=[{
                "name": tool_name,
                "args": {"query": query},
                "id": f"search_{hash(query) % 10000}"
            }]
        )

    def _create_blockchain_tool_call(self, analysis: Dict[str, Any]):
        """Create a tool call for blockchain analysis with fallback support."""
        from langchain_core.messages import AIMessage

        # Get address info from analysis
        address_info = analysis.get("address_info", {})
        if not address_info:
            # Fallback to old method
            wallet_address = analysis.get("wallet_address", "")
            if wallet_address.startswith("0x"):
                blockchain = "ethereum"
                address = wallet_address
                possible_blockchains = ["ethereum"]
            else:
                blockchain = "solana"
                address = wallet_address
                possible_blockchains = ["solana"]
        else:
            blockchain = address_info.get("blockchain", "ethereum")
            address = address_info.get("address", "")
            possible_blockchains = address_info.get("possible_blockchains", [blockchain])

        # Try the primary blockchain first
        tool_name, args = self._get_blockchain_tool_and_args(blockchain, address)

        return AIMessage(
            content="",
            tool_calls=[{
                "name": tool_name,
                "args": args,
                "id": f"blockchain_{hash(address) % 10000}"
            }]
        )

    def _create_token_analysis_tool_call(self, analysis: Dict[str, Any]):
        """Create a tool call for token analysis (Solana token metadata)."""
        from langchain_core.messages import AIMessage

        # Get address info from analysis
        address_info = analysis.get("address_info", {})
        address = address_info.get("address", "")

        # Start with token metadata to determine if it's a token contract
        tool_name = "solana_gettokenmetadata"
        args = {"address": address, "network": "mainnet"}

        return AIMessage(
            content="",
            tool_calls=[{
                "name": tool_name,
                "args": args,
                "id": f"token_metadata_{hash(address) % 10000}"
            }]
        )

    def _create_token_price_tool_call(self, analysis: Dict[str, Any]):
        """Create a tool call for token price analysis."""
        from langchain_core.messages import AIMessage

        address_info = analysis.get("address_info", {})
        address = address_info.get("address", "")

        return AIMessage(
            content="",
            tool_calls=[{
                "name": "solana_gettokenprice",
                "args": {"address": address, "network": "mainnet"},
                "id": f"token_price_{hash(address) % 10000}"
            }]
        )

    def _create_token_pairs_tool_call(self, analysis: Dict[str, Any]):
        """Create a tool call for token pairs analysis."""
        from langchain_core.messages import AIMessage

        address_info = analysis.get("address_info", {})
        address = address_info.get("address", "")

        return AIMessage(
            content="",
            tool_calls=[{
                "name": "solana_gettokenpairs",
                "args": {"address": address, "network": "mainnet", "limit": 10},
                "id": f"token_pairs_{hash(address) % 10000}"
            }]
        )

    def _create_token_swaps_tool_call(self, analysis: Dict[str, Any]):
        """Create a tool call for token swaps analysis."""
        from langchain_core.messages import AIMessage

        address_info = analysis.get("address_info", {})
        address = address_info.get("address", "")

        return AIMessage(
            content="",
            tool_calls=[{
                "name": "solana_getswapsbytokenaddress",
                "args": {
                    "address": address,
                    "network": "mainnet",
                    "limit": 10,
                    "transactionTypes": "buy,sell"
                },
                "id": f"token_swaps_{hash(address) % 10000}"
            }]
        )

    def _create_token_holders_tool_call(self, analysis: Dict[str, Any]):
        """Create a tool call for token holders analysis."""
        from langchain_core.messages import AIMessage

        address_info = analysis.get("address_info", {})
        address = address_info.get("address", "")

        return AIMessage(
            content="",
            tool_calls=[{
                "name": "solana_gettokenholders",
                "args": {"address": address, "network": "mainnet"},
                "id": f"token_holders_{hash(address) % 10000}"
            }]
        )

    def _create_wallet_analysis_tool_call(self, analysis: Dict[str, Any]):
        """Create a tool call for comprehensive wallet portfolio analysis."""
        from langchain_core.messages import AIMessage

        address_info = analysis.get("address_info", {})
        address = address_info.get("address", "")
        blockchain = address_info.get("blockchain", "solana")

        if blockchain == "solana":
            tool_name = "solana_getportfolio"
            args = {
                "address": address,
                "network": "mainnet",
                "excludeSpam": True,
                "mediaItems": True,
                "nftMetadata": True
            }
        else:
            # For Ethereum, use wallet token balances
            tool_name = "evm_getwallettokenbalancesprice"
            args = {"address": address}

        return AIMessage(
            content="",
            tool_calls=[{
                "name": tool_name,
                "args": args,
                "id": f"wallet_portfolio_{hash(address) % 10000}"
            }]
        )

    def _create_native_balance_tool_call(self, analysis: Dict[str, Any]):
        """Create a tool call for native balance verification."""
        from langchain_core.messages import AIMessage

        address_info = analysis.get("address_info", {})
        address = address_info.get("address", "")
        blockchain = address_info.get("blockchain", "solana")

        if blockchain == "solana":
            tool_name = "solana_balance"
            args = {"address": address, "network": "mainnet"}
        else:
            # For Ethereum, native balance is included in portfolio call
            tool_name = "evm_getnativebalancesforaddresses"
            args = {"wallet_addresses": [address]}

        return AIMessage(
            content="",
            tool_calls=[{
                "name": tool_name,
                "args": args,
                "id": f"native_balance_{hash(address) % 10000}"
            }]
        )

    def _create_trading_history_tool_call(self, analysis: Dict[str, Any]):
        """Create a tool call for trading history analysis."""
        from langchain_core.messages import AIMessage

        address_info = analysis.get("address_info", {})
        address = address_info.get("address", "")
        blockchain = address_info.get("blockchain", "solana")

        if blockchain == "solana":
            tool_name = "solana_getswapsbywalletaddress"
            args = {
                "address": address,
                "network": "mainnet",
                "limit": 10
            }
        else:
            # For Ethereum, use wallet history
            tool_name = "evm_getwallethistory"
            args = {"address": address, "limit": 10}

        return AIMessage(
            content="",
            tool_calls=[{
                "name": tool_name,
                "args": args,
                "id": f"trading_history_{hash(address) % 10000}"
            }]
        )

    def _get_blockchain_tool_and_args(self, blockchain: str, address: str) -> tuple:
        """Get the appropriate tool name and arguments for a blockchain."""
        if blockchain == "ethereum":
            return "evm_getwallettokenbalancesprice", {"address": address}
        elif blockchain == "solana":
            return "solana_getportfolio", {"address": address, "network": "mainnet"}
        elif blockchain in ["bitcoin", "bitcoin_segwit"]:
            # For Bitcoin, we might need to use a different approach or web search
            return "evm_getwallettokenbalancesprice", {"address": address}  # Will fail, triggering web search
        else:
            # Default to Ethereum, will fail and trigger alternatives
            return "evm_getwallettokenbalancesprice", {"address": address}

    def _create_thinking_tool_call(self, analysis: Dict[str, Any]):
        """Create a tool call for sequential thinking."""
        from langchain_core.messages import AIMessage

        thinking_prompt = analysis.get("thinking_prompt", "Analyze the gathered data and provide strategic insights.")

        return AIMessage(
            content="",
            tool_calls=[{
                "name": "sequentialthinking",
                "args": {
                    "thought": thinking_prompt,
                    "nextThoughtNeeded": True,
                    "thoughtNumber": 1,
                    "totalThoughts": 3
                },
                "id": f"thinking_{hash(thinking_prompt) % 10000}"
            }]
        )

    def _create_thinking_prompt(self, state: AgentState) -> str:
        """Create a prompt for sequential thinking based on current state."""
        user_input = state.get("input", "")
        intent = state.get("intent", "")
        information_gathered = state.get("information_gathered", {})

        # Create context-aware thinking prompt
        if intent == "address_analysis":
            prompt = f"I need to analyze the blockchain address: {user_input}\n\n"
        else:
            prompt = f"User request: {user_input}\n\n"

        prompt += "Available data to analyze:\n"

        if information_gathered.get("web_data"):
            web_data = information_gathered['web_data'][:800]
            prompt += f"Web research findings: {web_data}...\n\n"

        if information_gathered.get("blockchain_data"):
            blockchain_data = information_gathered['blockchain_data'][:800]
            prompt += f"Blockchain analysis results: {blockchain_data}...\n\n"

        # Specific analysis request based on intent
        if intent == "address_analysis":
            prompt += "Please provide strategic analysis including:\n"
            prompt += "1. What type of address this is (wallet, contract, etc.)\n"
            prompt += "2. Activity patterns and risk assessment\n"
            prompt += "3. Portfolio composition and recommendations\n"
            prompt += "4. Any notable insights or red flags"
        else:
            prompt += "Please analyze this information step by step and provide strategic insights, recommendations, or conclusions."

        return prompt

    async def invoke(self, message: str, execution_profile: str = "balanced", config: RunnableConfig = None) -> Dict[str, Any]:
        """100x Enhanced invoke method with performance tracking and optimization."""
        if not self.graph:
            raise RuntimeError("Agent not initialized. Call initialize() first.")

        start_time = time.time()
        print(f"\n🚀 100x Enhanced Agent Processing: {message[:100]}...")
        print(f"📊 Execution Profile: {execution_profile}")

        if config is None:
            config = RunnableConfig(
                recursion_limit=50,  # Higher limit for complex reasoning
                thread_id="enhanced_agent"
            )

        # Create initial state with 100x enhancements
        initial_state = {
            "input": message,
            "intent": "",
            "final_response": "",
            "plan": [],
            "current_step": 0,
            "reasoning_history": [],
            "messages": [],
            "tool_outputs": [],
            "information_gathered": {},
            "next_action": "",
            "confidence_level": 0.0,
            "execution_profile": execution_profile,
            "last_tool_output_quality": "good",
            "error_count": 0,
            "reflection_depth": 0,
            "planning_cycles": 0,
            "start_time": start_time,  # For performance tracking

            # 100x Enhancement fields
            "active_specialist": "",
            "specialist_context": {},
            "swarm_coordination": {},
            "performance_metrics": PerformanceMetrics() if not hasattr(self, 'performance_tracker') else self.performance_tracker.performance_metrics,
            "graph_optimizations": [],
            "meta_reflection_triggered": False,
            "multi_modal_context": MultiModalContext(),
            "generated_tools": {},
            "tool_generation_history": [],
            "sandbox_results": {},
            "pending_approvals": [],
            "user_preferences": {},
            "risk_tolerance": "moderate",
            "memory_context": {},
            "learning_insights": [],
            "performance_history": {},
            "autonomous_goals": [],
            "scheduled_tasks": [],
            "monitoring_targets": [],
            "speculative_results": {},
            "parallel_executions": [],
            "model_tier_used": execution_profile
        }

        # Run the graph
        result = await self.graph.ainvoke(initial_state, config=config)

        # Performance tracking
        execution_time = time.time() - start_time
        success = result.get('confidence_level', 0.0) > 0.7

        if self.performance_tracker:
            self.performance_tracker.record_query_pattern(result, success, execution_time)

        # Enhanced result display
        print(f"\n✅ 100x Enhanced Analysis Complete!")
        print(f"🎯 Intent: {result.get('intent', 'Unknown')}")
        print(f"📈 Confidence: {result.get('confidence_level', 0.0):.2f}")
        print(f"🧠 Reasoning Steps: {len(result.get('reasoning_history', []))}")
        print(f"⏱️  Execution Time: {execution_time:.2f}s")

        # Show 100x enhancement status
        if result.get('meta_reflection_triggered'):
            print(f"🧠 Meta-Reflection: Graph optimization analyzed")

        if result.get('swarm_coordination', {}).get('coordination_used'):
            print(f"🚀 Swarm Coordination: Used specialist agents")

        if result.get('graph_optimizations'):
            print(f"⚡ Graph Optimizations: {len(result.get('graph_optimizations', []))} applied")

        return result

    async def _process_with_react_agent(self, user_input: str, address_info: dict) -> str:
        """Process address analysis using the React agent for proper MCP tool execution."""
        blockchain = address_info.get("blockchain", "unknown")
        address = address_info.get("address", "")

        if blockchain == "solana":
            prompt = f"""
            Analyze the Solana address: {address}

            FOLLOW THIS EXACT SYSTEMATIC PROCESS - EXECUTE ALL STEPS COMPLETELY:

            STEP 1: TOKEN IDENTIFICATION
            - Call solana_gettokenmetadata with network="mainnet" and address="{address}"
            - If successful (not 404), this is a TOKEN CONTRACT - proceed to COMPLETE TOKEN ANALYSIS
            - If 404 error, this is a WALLET ADDRESS - proceed to COMPLETE WALLET ANALYSIS

            ===== IF TOKEN CONTRACT - EXECUTE ALL 5 STEPS =====

            STEP 2: TOKEN PRICE & MARKET DATA
            - Call solana_gettokenprice with network="mainnet" and address="{address}"
            - Extract current USD price, 24h change, market cap, volume

            STEP 3: TRADING PAIRS & LIQUIDITY ANALYSIS
            - Call solana_gettokenpairs with network="mainnet", address="{address}", limit=10
            - Analyze ALL trading pairs, exchanges (Raydium, Orca), liquidity amounts
            - Identify primary and secondary pairs

            STEP 4: TRADING ACTIVITY ANALYSIS
            - Call solana_getswapsbytokenaddress with network="mainnet", address="{address}", limit=20, transactionTypes="buy,sell"
            - Analyze recent transactions, buy/sell patterns, transaction sizes
            - Calculate trading volume and activity trends

            STEP 5: HOLDER DISTRIBUTION ANALYSIS
            - Call solana_gettokenholders with network="mainnet" and address="{address}"
            - Call solana_gettopholders with network="mainnet", address="{address}", limit=50
            - Analyze total holders, concentration risk, holder trends, distribution

            PROVIDE COMPREHENSIVE TOKEN ANALYSIS INCLUDING:
            - Basic Information (name, symbol, decimals, supply)
            - Current Market Data (price, volume, market cap, 24h change)
            - Trading Pairs & Liquidity (all exchanges, liquidity amounts)
            - Holder Analysis (total holders, concentration, trends)
            - Recent Trading Activity (volume, patterns, notable trades)
            - Risk Assessment (concentration risk, liquidity risk, market risk)
            - Project Information (if available from metadata)

            ===== IF WALLET ADDRESS - EXECUTE ALL 9 STEPS =====

            STEP 2: PORTFOLIO ANALYSIS
            - Call solana_getportfolio with network="mainnet", address="{address}", excludeSpam=true, mediaItems=true, nftMetadata=true
            - Extract ALL token holdings with quantities and values
            - List ALL NFTs with metadata

            STEP 3: NATIVE BALANCE VERIFICATION
            - Call solana_balance with network="mainnet" and address="{address}"
            - Convert lamports to SOL and calculate USD value

            STEP 4: TRADING HISTORY ANALYSIS
            - Call solana_getswapsbywalletaddress with network="mainnet", address="{address}", limit=20
            - Analyze trading patterns, exchanges used, profit/loss calculations
            - Identify trading strategy and behavior patterns

            STEP 5: TOKEN HOLDINGS DEEP DIVE
            - For each significant token holding, call solana_gettokenprice to get current values
            - Calculate portfolio composition and allocation percentages

            STEP 6: TRANSACTION PATTERN ANALYSIS
            - Analyze transaction frequency, timing patterns
            - Identify preferred exchanges and trading pairs
            - Calculate average transaction sizes

            STEP 7: PROFIT/LOSS ANALYSIS
            - Calculate realized and unrealized gains/losses where possible
            - Identify most profitable and least profitable trades

            STEP 8: RISK PROFILE ASSESSMENT
            - Analyze portfolio diversification
            - Assess exposure to different token categories
            - Evaluate trading risk level

            STEP 9: TRADER PROFILE CLASSIFICATION
            - Classify trader type (day trader, swing trader, holder, etc.)
            - Identify specialization areas (meme tokens, DeFi, NFTs, etc.)

            PROVIDE COMPREHENSIVE WALLET ANALYSIS INCLUDING:
            - Current Holdings (SOL balance, all tokens with quantities and values, NFTs)
            - Portfolio Composition (allocation percentages, diversification analysis)
            - Trading Activity Analysis (recent trades, patterns, exchanges used)
            - Notable Trades (most profitable, largest positions, recent activity)
            - Wallet Characteristics (trader profile, risk level, strategy, activity level)
            - Risk Assessment and Recommendations

            IMPORTANT: Execute ALL steps completely. Do not stop early. Provide detailed analysis with specific numbers, percentages, and actionable insights.

            FOR TOKEN ANALYSIS INCLUDE:
            - Basic Information (name, symbol, decimals, total supply)
            - Current Market Data (price, 24h change, volume, market cap)
            - Trading Pairs & Liquidity (all exchanges, liquidity amounts, primary pairs)
            - Holder Analysis (total holders, concentration risk, top holder percentages)
            - Recent Trading Activity (buy/sell volume, transaction patterns, notable trades)
            - Risk Assessment (concentration risk, liquidity risk, market volatility)
            - Project Information (website, social media, tokenomics if available)

            FOR WALLET ANALYSIS INCLUDE:
            - Current Holdings (SOL balance in USD, all tokens with quantities and values, NFTs)
            - Portfolio Composition (allocation percentages, diversification analysis)
            - Trading Activity Analysis (recent trades, patterns, preferred exchanges)
            - Notable Trades (most profitable trades, largest positions, recent activity)
            - Wallet Characteristics (trader profile, risk level, strategy, activity level)
            - Risk Assessment and Investment Recommendations

            CRITICAL: Use the actual MCP tools with exact parameters shown. Execute every single step. Provide comprehensive analysis like a professional crypto analyst.
            """
        elif blockchain == "ethereum":
            prompt = f"""
            Analyze the Ethereum address: {address}

            FOLLOW THIS SYSTEMATIC ETHEREUM ANALYSIS PROCESS:

            STEP 1: WALLET TOKEN ANALYSIS
            - Call evm_getwallettokenbalancesprice with address="{address}"
            - Extract all ERC20 token holdings, balances, USD values

            STEP 2: NFT COLLECTION ANALYSIS
            - Call evm_getwalletnfts with address="{address}", limit=20
            - Analyze NFT holdings, collections, floor prices

            STEP 3: TRANSACTION HISTORY ANALYSIS
            - Call evm_getwallethistory with address="{address}", limit=10
            - Analyze transaction patterns, DeFi activity, trading behavior

            STEP 4: COMPREHENSIVE ANALYSIS
            - Provide detailed portfolio breakdown
            - Assess risk factors and wallet characteristics
            - Include actionable insights and recommendations

            Use the actual MCP tools with exact parameters shown.
            """
        else:
            prompt = f"Analyze the {blockchain} address: {address} using appropriate tools."

        # Configure the React agent
        config = RunnableConfig(recursion_limit=30, thread_id=1)

        # Execute with the React agent
        result = await self.react_agent.ainvoke(
            {"messages": [HumanMessage(content=prompt)]},
            config=config
        )

        # Extract the final response
        if result and "messages" in result:
            final_message = result["messages"][-1]
            return final_message.content
        else:
            return "No response from React agent"

    async def stream(self, message: str, config: RunnableConfig = None):
        """Stream the enhanced agent response."""
        if not self.graph:
            raise RuntimeError("Agent not initialized. Call initialize() first.")

        if config is None:
            config = RunnableConfig(
                recursion_limit=50,
                thread_id="enhanced_agent"
            )

        # Create initial state
        initial_state = {
            "input": message,
            "intent": "",
            "final_response": "",
            "plan": [],
            "current_step": 0,
            "reasoning_history": [],
            "messages": [],
            "tool_outputs": [],
            "information_gathered": {},
            "next_action": "",
            "confidence_level": 0.0,
            "planning_cycles": 0
        }

        async for chunk in self.graph.astream(initial_state, config=config):
            yield chunk

    def _process_tool_outputs(self, state: AgentState) -> AgentState:
        """Process tool outputs and update information_gathered."""
        from langchain_core.messages import ToolMessage
        import json

        messages = state.get("messages", [])
        information_gathered = state.get("information_gathered", {})

        # Look for tool messages in the conversation
        for message in messages:
            if isinstance(message, ToolMessage):
                # Get tool name from the message
                tool_name = getattr(message, 'name', '')
                if not tool_name:
                    # Try to get from tool_call_id or other attributes
                    tool_call_id = getattr(message, 'tool_call_id', '')
                    if 'search' in tool_call_id or 'tavily' in tool_call_id:
                        tool_name = 'search'
                    elif 'blockchain' in tool_call_id:
                        tool_name = 'blockchain'
                    elif 'thinking' in tool_call_id:
                        tool_name = 'sequentialthinking'

                content = message.content

                # Process and summarize tool outputs for better user experience
                processed_content = self._process_tool_content(tool_name, content)

                # Categorize tool outputs based on content and tool name
                if any(search_tool in tool_name.lower() for search_tool in ['tavily_search', 'search', 'tavily']):
                    information_gathered["web_data"] = processed_content
                elif "solana_gettokenmetadata" in tool_name.lower():
                    # Store raw content for blockchain tools to preserve JSON structure
                    information_gathered["token_metadata"] = content
                    # Determine if it's a token or wallet based on response
                    if "404" in content or "not found" in content.lower():
                        information_gathered["is_token"] = False
                    else:
                        information_gathered["is_token"] = True
                elif "solana_gettokenprice" in tool_name.lower():
                    information_gathered["token_price"] = content
                elif "solana_gettokenpairs" in tool_name.lower():
                    information_gathered["token_pairs"] = content
                elif "solana_getswapsbytokenaddress" in tool_name.lower():
                    information_gathered["token_swaps"] = content
                elif "solana_gettokenholders" in tool_name.lower():
                    information_gathered["token_holders"] = content
                elif "solana_gettopholders" in tool_name.lower():
                    information_gathered["top_holders"] = content
                elif "solana_getportfolio" in tool_name.lower():
                    information_gathered["portfolio_data"] = content
                elif "solana_balance" in tool_name.lower():
                    information_gathered["native_balance"] = content
                elif "solana_getswapsbywalletaddress" in tool_name.lower():
                    information_gathered["wallet_swaps"] = content
                elif "evm_getwallettokenbalancesprice" in tool_name.lower():
                    information_gathered["wallet_tokens"] = content
                elif "evm_getwalletnfts" in tool_name.lower():
                    information_gathered["wallet_nfts"] = content
                elif "evm_getwallethistory" in tool_name.lower():
                    information_gathered["wallet_history"] = content
                elif 'gettokenmetadata' in tool_name.lower():
                    information_gathered["token_metadata"] = processed_content
                    # If token metadata is successful, we need more token analysis
                    if 'error' not in content.lower() and 'not found' not in content.lower():
                        information_gathered["is_token_contract"] = True
                elif 'gettokenprice' in tool_name.lower():
                    information_gathered["token_price"] = processed_content
                elif 'gettokenpairs' in tool_name.lower():
                    information_gathered["token_pairs"] = processed_content
                elif 'getswapsbytokenaddress' in tool_name.lower():
                    information_gathered["token_swaps"] = processed_content
                elif 'gettokenholders' in tool_name.lower():
                    information_gathered["token_holders"] = processed_content
                elif 'getportfolio' in tool_name.lower():
                    information_gathered["wallet_portfolio"] = processed_content
                elif 'balance' in tool_name.lower() and 'native' not in tool_name.lower():
                    information_gathered["native_balance"] = processed_content
                elif 'getswapsbywalletaddress' in tool_name.lower() or 'getwallethistory' in tool_name.lower():
                    information_gathered["trading_history"] = processed_content
                elif any(blockchain_tool in tool_name.lower() for blockchain_tool in ['evm_', 'solana_', 'blockchain']):
                    information_gathered["blockchain_data"] = processed_content
                elif 'sequentialthinking' in tool_name.lower() or 'thinking' in tool_name.lower():
                    information_gathered["strategic_analysis"] = processed_content
                elif 'error' not in content.lower():
                    # If we can't categorize but it's not an error, try to infer from content
                    if any(keyword in content.lower() for keyword in ['balance', 'token', 'nft', 'wallet', 'address']):
                        information_gathered["blockchain_data"] = processed_content
                    elif any(keyword in content.lower() for keyword in ['search', 'news', 'article', 'website']):
                        information_gathered["web_data"] = processed_content

        return {**state, "information_gathered": information_gathered}

    def _process_tool_content(self, tool_name: str, content: str) -> str:
        """Process raw tool content into user-friendly summaries."""
        import json
        import re

        try:
            # Handle MCP tool responses that include API status
            if "API Response (Status:" in content:
                # Extract JSON from the response
                json_match = re.search(r'\{.*\}', content, re.DOTALL)
                if json_match:
                    json_content = json_match.group(0)
                    data = json.loads(json_content)
                else:
                    return content

            # Try to parse JSON content directly
            elif content.startswith('{') or content.startswith('['):
                data = json.loads(content)

            else:
                # Content is already text, return as-is
                return content

            # Process different types of data
            if any(blockchain_tool in tool_name.lower() for blockchain_tool in ['evm_', 'solana_', 'blockchain']):
                return self._summarize_blockchain_data(data)

            elif any(search_tool in tool_name.lower() for search_tool in ['tavily', 'search']):
                return self._summarize_search_data(data)

            else:
                return self._summarize_generic_data(data)

        except (json.JSONDecodeError, Exception) as e:
            # If parsing fails, try to extract useful info from text
            if any(blockchain_tool in tool_name.lower() for blockchain_tool in ['evm_', 'solana_', 'blockchain']):
                return self._extract_blockchain_summary_from_text(content)
            else:
                return content[:300] + "..." if len(content) > 300 else content

    def _summarize_blockchain_data(self, data) -> str:
        """Summarize blockchain data into user-friendly format."""
        if isinstance(data, dict):
            summary = []

            # Handle wallet balance data
            if 'result' in data and isinstance(data['result'], list):
                tokens = data['result']
                if tokens:
                    summary.append(f"Wallet contains {len(tokens)} token(s):")
                    for token in tokens[:5]:  # Show first 5 tokens
                        name = token.get('name', 'Unknown')
                        symbol = token.get('symbol', '')
                        balance = token.get('balance_formatted', token.get('balance', '0'))
                        usd_value = token.get('usd_value', 0)
                        summary.append(f"  • {name} ({symbol}): {balance} (${usd_value:.2f})")
                    if len(tokens) > 5:
                        summary.append(f"  ... and {len(tokens) - 5} more tokens")
                else:
                    summary.append("Wallet appears to be empty or contains no tokens")

            # Handle NFT data
            elif 'result' in data and 'total' in data:
                total = data.get('total', 0)
                if total > 0:
                    summary.append(f"Wallet contains {total} NFT(s)")
                    nfts = data.get('result', [])
                    for nft in nfts[:3]:  # Show first 3 NFTs
                        name = nft.get('name', 'Unknown NFT')
                        collection = nft.get('token_address', '')[:10] + '...'
                        summary.append(f"  • {name} (Collection: {collection})")
                else:
                    summary.append("No NFTs found in this wallet")

            # Handle Solana portfolio data
            elif 'tokens' in data or 'nfts' in data:
                if 'tokens' in data:
                    tokens = data['tokens']
                    summary.append(f"Solana wallet contains {len(tokens)} token(s)")
                if 'nfts' in data:
                    nfts = data['nfts']
                    summary.append(f"Contains {len(nfts)} NFT(s)")

            return '\n'.join(summary) if summary else str(data)[:200] + "..."

        return str(data)[:200] + "..."

    def _summarize_search_data(self, data) -> str:
        """Summarize search results into user-friendly format."""
        if isinstance(data, list):
            summary = [f"Found {len(data)} search result(s):"]
            for i, result in enumerate(data[:3], 1):  # Show first 3 results
                if isinstance(result, dict):
                    title = result.get('title', result.get('name', f'Result {i}'))
                    url = result.get('url', result.get('link', ''))
                    content = result.get('content', result.get('snippet', ''))[:100]
                    summary.append(f"{i}. {title}")
                    if content:
                        summary.append(f"   {content}...")
            return '\n'.join(summary)

        elif isinstance(data, dict):
            if 'results' in data:
                return self._summarize_search_data(data['results'])
            else:
                title = data.get('title', 'Search Result')
                content = data.get('content', str(data))[:200]
                return f"{title}: {content}..."

        return str(data)[:200] + "..."

    def _summarize_generic_data(self, data) -> str:
        """Summarize generic structured data."""
        if isinstance(data, dict):
            if len(data) <= 3:
                return str(data)
            else:
                keys = list(data.keys())[:3]
                summary = {k: data[k] for k in keys}
                return f"{summary}... (and {len(data) - 3} more fields)"
        elif isinstance(data, list):
            if len(data) <= 3:
                return str(data)
            else:
                return f"[{data[0]}, {data[1]}, {data[2]}, ... and {len(data) - 3} more items]"
        else:
            return str(data)[:200] + "..."

    def _extract_blockchain_summary_from_text(self, content: str) -> str:
        """Extract useful blockchain information from text content."""
        import re

        summary = []

        # Look for token information in the text
        token_matches = re.findall(r'"symbol":\s*"([^"]+)".*?"balance_formatted":\s*"([^"]+)".*?"usd_value":\s*([0-9.]+)', content, re.DOTALL)

        if token_matches:
            summary.append("Wallet Token Holdings:")
            total_value = 0
            for symbol, balance, usd_value in token_matches:
                usd_val = float(usd_value)
                total_value += usd_val
                if usd_val > 0.01:  # Only show tokens with meaningful value
                    summary.append(f"  • {symbol}: {balance} (${usd_val:.2f})")

            summary.append(f"\nTotal Portfolio Value: ${total_value:.2f}")

        # Look for NFT information
        nft_matches = re.findall(r'"total":\s*(\d+)', content)
        if nft_matches:
            total_nfts = nft_matches[0]
            summary.append(f"NFTs: {total_nfts} total")

        return '\n'.join(summary) if summary else "Blockchain data processed successfully"

    async def cleanup(self):
        """Cleanup resources."""
        if self.client:
            print("✅ MCP client cleaned up")

# Utility function for easy agent creation
async def create_enhanced_agent(server_configs: Dict[str, Dict[str, Any]] = None) -> EnhancedLangGraphMCPAgent:
    """Create and initialize an Enhanced LangGraph MCP agent."""
    agent = EnhancedLangGraphMCPAgent()
    await agent.initialize(server_configs)
    return agent

# Example usage
async def main():
    """Example usage of the Enhanced LangGraph MCP agent."""
    agent = None
    try:
        # Create and initialize the enhanced agent
        agent = await create_enhanced_agent()

        # List available tools
        print("\n📋 Available tools:")
        for tool in agent.list_tools():
            print(f"  - {tool}")

        # Example complex multi-step query
        complex_query = """Search for recent Bitcoin news, analyze wallet ******************************************,
        and use sequential thinking to provide investment recommendations based on the market context."""

        print(f"\n🤖 Running complex query:")
        print(f"Query: {complex_query}")

        response = await agent.invoke(complex_query)

        print(f"\n📊 Final Response:")
        print(response.get("final_response", "No response generated"))

        print(f"\n🧠 Reasoning Process:")
        for step in response.get("reasoning_history", []):
            print(f"  - {step}")

    except Exception as e:
        print(f"❌ Error: {e}")

    finally:
        if agent:
            await agent.cleanup()

if __name__ == "__main__":
    import asyncio
    asyncio.run(main())
 