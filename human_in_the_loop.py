#!/usr/bin/env python3
"""
Human-in-the-Loop System for Phase 3 Enhanced Capabilities
Interactive approval workflows for high-risk operations and collaborative decision making.
"""

import asyncio
import json
from typing import Dict, Any, List, Optional, Callable, Literal
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from enum import Enum
import threading
import queue

class RiskLevel(Enum):
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"

class ApprovalStatus(Enum):
    PENDING = "pending"
    APPROVED = "approved"
    DENIED = "denied"
    TIMEOUT = "timeout"
    MODIFIED = "modified"

@dataclass
class ApprovalRequest:
    """Request for human approval."""
    request_id: str
    operation_type: str
    description: str
    risk_level: RiskLevel
    risk_factors: List[str]
    potential_impact: str
    recommended_action: str
    context: Dict[str, Any] = field(default_factory=dict)
    timeout_seconds: int = 300  # 5 minutes default
    created_at: datetime = field(default_factory=datetime.now)
    status: ApprovalStatus = ApprovalStatus.PENDING
    user_response: Optional[str] = None
    user_modifications: Dict[str, Any] = field(default_factory=dict)
    auto_approve_eligible: bool = False

@dataclass
class UserPreferences:
    """User preferences for approval workflows."""
    risk_tolerance: RiskLevel = RiskLevel.MEDIUM
    auto_approve_low_risk: bool = True
    auto_approve_threshold: float = 1000.0  # USD value
    trusted_protocols: List[str] = field(default_factory=list)
    notification_preferences: Dict[str, bool] = field(default_factory=lambda: {
        "email": False,
        "console": True,
        "sound": False
    })
    approval_timeout: int = 300  # seconds
    require_confirmation_for: List[str] = field(default_factory=lambda: [
        "high_value_transactions",
        "new_protocols",
        "experimental_tools"
    ])

class RiskAssessment:
    """Risk assessment engine for operations."""
    
    def __init__(self):
        self.risk_factors = {
            "financial": {
                "high_value": {"threshold": 10000, "weight": 0.4},
                "medium_value": {"threshold": 1000, "weight": 0.2},
                "low_value": {"threshold": 100, "weight": 0.1}
            },
            "security": {
                "new_protocol": {"weight": 0.3},
                "unverified_contract": {"weight": 0.5},
                "experimental_tool": {"weight": 0.4}
            },
            "operational": {
                "irreversible_action": {"weight": 0.6},
                "data_exposure": {"weight": 0.3},
                "system_modification": {"weight": 0.4}
            }
        }
    
    def assess_risk(self, operation: Dict[str, Any]) -> Dict[str, Any]:
        """Assess risk level for an operation."""
        
        risk_score = 0.0
        risk_factors = []
        
        # Financial risk assessment
        value = operation.get("value", 0)
        if value > 10000:
            risk_score += 0.4
            risk_factors.append("high_value_transaction")
        elif value > 1000:
            risk_score += 0.2
            risk_factors.append("medium_value_transaction")
        
        # Security risk assessment
        if operation.get("new_protocol", False):
            risk_score += 0.3
            risk_factors.append("new_protocol_interaction")
        
        if operation.get("unverified_contract", False):
            risk_score += 0.5
            risk_factors.append("unverified_smart_contract")
        
        if operation.get("experimental_tool", False):
            risk_score += 0.4
            risk_factors.append("experimental_tool_usage")
        
        # Operational risk assessment
        if operation.get("irreversible", False):
            risk_score += 0.6
            risk_factors.append("irreversible_operation")
        
        # Determine risk level
        if risk_score >= 0.8:
            risk_level = RiskLevel.CRITICAL
        elif risk_score >= 0.6:
            risk_level = RiskLevel.HIGH
        elif risk_score >= 0.3:
            risk_level = RiskLevel.MEDIUM
        else:
            risk_level = RiskLevel.LOW
        
        return {
            "risk_level": risk_level,
            "risk_score": risk_score,
            "risk_factors": risk_factors,
            "assessment_details": {
                "financial_risk": value,
                "security_concerns": len([f for f in risk_factors if "protocol" in f or "contract" in f]),
                "operational_impact": len([f for f in risk_factors if "irreversible" in f or "modification" in f])
            }
        }

class HumanInTheLoopSystem:
    """Human-in-the-Loop system for collaborative decision making."""
    
    def __init__(self):
        self.user_preferences = UserPreferences()
        self.risk_assessor = RiskAssessment()
        self.pending_requests: Dict[str, ApprovalRequest] = {}
        self.approval_history: List[ApprovalRequest] = []
        self.approval_queue = queue.Queue()
        self.response_queue = queue.Queue()
        
        # Start approval monitoring thread
        self.monitoring_active = True
        self.monitor_thread = threading.Thread(target=self._monitor_approvals, daemon=True)
        self.monitor_thread.start()
    
    async def request_approval(self, operation: Dict[str, Any]) -> ApprovalRequest:
        """Request human approval for an operation."""
        
        # Assess risk
        risk_assessment = self.risk_assessor.assess_risk(operation)
        
        # Create approval request
        request = ApprovalRequest(
            request_id=self._generate_request_id(),
            operation_type=operation.get("type", "unknown"),
            description=operation.get("description", "Operation requires approval"),
            risk_level=risk_assessment["risk_level"],
            risk_factors=risk_assessment["risk_factors"],
            potential_impact=operation.get("impact", "Unknown impact"),
            recommended_action=self._get_recommended_action(risk_assessment),
            context=operation,
            timeout_seconds=self.user_preferences.approval_timeout
        )
        
        # Check if auto-approval is possible
        if self._can_auto_approve(request):
            request.status = ApprovalStatus.APPROVED
            request.auto_approve_eligible = True
            print(f"✅ Auto-approved: {request.description}")
            return request
        
        # Add to pending requests
        self.pending_requests[request.request_id] = request
        
        # Display approval request to user
        await self._display_approval_request(request)
        
        # Wait for response or timeout
        response = await self._wait_for_response(request)
        
        # Update request status
        request.status = response["status"]
        request.user_response = response.get("response")
        request.user_modifications = response.get("modifications", {})
        
        # Move to history
        self.approval_history.append(request)
        if request.request_id in self.pending_requests:
            del self.pending_requests[request.request_id]
        
        return request
    
    def _can_auto_approve(self, request: ApprovalRequest) -> bool:
        """Check if request can be auto-approved based on user preferences."""
        
        # Check risk tolerance
        if request.risk_level.value == "low" and self.user_preferences.auto_approve_low_risk:
            return True
        
        # Check value threshold
        value = request.context.get("value", 0)
        if value <= self.user_preferences.auto_approve_threshold and request.risk_level != RiskLevel.CRITICAL:
            return True
        
        # Check trusted protocols
        protocol = request.context.get("protocol", "")
        if protocol in self.user_preferences.trusted_protocols and request.risk_level != RiskLevel.HIGH:
            return True
        
        return False
    
    async def _display_approval_request(self, request: ApprovalRequest):
        """Display approval request to user."""
        
        print(f"\n{'='*60}")
        print(f"🚨 APPROVAL REQUIRED - {request.operation_type.upper()}")
        print(f"{'='*60}")
        print(f"📋 Description: {request.description}")
        print(f"⚠️  Risk Level: {request.risk_level.value.upper()}")
        print(f"🔍 Risk Factors: {', '.join(request.risk_factors)}")
        print(f"💥 Potential Impact: {request.potential_impact}")
        print(f"💡 Recommended Action: {request.recommended_action}")
        
        if request.context.get("value"):
            print(f"💰 Value: ${request.context['value']:,.2f}")
        
        if request.context.get("protocol"):
            print(f"🏛️  Protocol: {request.context['protocol']}")
        
        print(f"\n⏰ Timeout: {request.timeout_seconds} seconds")
        print(f"🆔 Request ID: {request.request_id}")
        
        print(f"\n{'='*60}")
        print("RESPONSE OPTIONS:")
        print("1. 'approve' - Approve the operation")
        print("2. 'deny' - Deny the operation")
        print("3. 'modify:<changes>' - Approve with modifications")
        print("4. 'info' - Request more information")
        print(f"{'='*60}")
        
        # Add to approval queue for monitoring
        self.approval_queue.put(request)
    
    async def _wait_for_response(self, request: ApprovalRequest) -> Dict[str, Any]:
        """Wait for user response or timeout."""
        
        start_time = datetime.now()
        timeout_time = start_time + timedelta(seconds=request.timeout_seconds)
        
        while datetime.now() < timeout_time:
            try:
                # Check for response (in real implementation, this would be from UI/CLI input)
                response = await self._get_user_input(request.request_id, timeout=1.0)
                if response:
                    return self._parse_user_response(response)
            except asyncio.TimeoutError:
                continue
            
            await asyncio.sleep(0.1)
        
        # Timeout occurred
        print(f"⏰ Approval request {request.request_id} timed out")
        return {
            "status": ApprovalStatus.TIMEOUT,
            "response": "Request timed out"
        }
    
    async def _get_user_input(self, request_id: str, timeout: float = 1.0) -> Optional[str]:
        """Get user input (placeholder - would integrate with actual UI/CLI)."""
        
        # Placeholder implementation - in real system would get actual user input
        # For demo purposes, simulate some responses
        
        # This would be replaced with actual input mechanism
        return None
    
    def _parse_user_response(self, response: str) -> Dict[str, Any]:
        """Parse user response into structured format."""
        
        response = response.strip().lower()
        
        if response == "approve":
            return {"status": ApprovalStatus.APPROVED}
        elif response == "deny":
            return {"status": ApprovalStatus.DENIED}
        elif response.startswith("modify:"):
            modifications = response[7:].strip()
            return {
                "status": ApprovalStatus.MODIFIED,
                "modifications": {"user_input": modifications}
            }
        else:
            return {"status": ApprovalStatus.DENIED, "response": "Invalid response"}
    
    def _get_recommended_action(self, risk_assessment: Dict[str, Any]) -> str:
        """Get recommended action based on risk assessment."""
        
        risk_level = risk_assessment["risk_level"]
        
        if risk_level == RiskLevel.CRITICAL:
            return "DENY - Critical risk detected"
        elif risk_level == RiskLevel.HIGH:
            return "REVIEW CAREFULLY - High risk operation"
        elif risk_level == RiskLevel.MEDIUM:
            return "PROCEED WITH CAUTION - Medium risk"
        else:
            return "APPROVE - Low risk operation"
    
    def _generate_request_id(self) -> str:
        """Generate unique request ID."""
        import uuid
        return str(uuid.uuid4())[:8]
    
    def _monitor_approvals(self):
        """Monitor approval requests in background thread."""
        
        while self.monitoring_active:
            try:
                # Check for expired requests
                current_time = datetime.now()
                expired_requests = []
                
                for request_id, request in self.pending_requests.items():
                    if current_time > request.created_at + timedelta(seconds=request.timeout_seconds):
                        expired_requests.append(request_id)
                
                # Handle expired requests
                for request_id in expired_requests:
                    request = self.pending_requests[request_id]
                    request.status = ApprovalStatus.TIMEOUT
                    self.approval_history.append(request)
                    del self.pending_requests[request_id]
                    print(f"⏰ Request {request_id} expired")
                
                # Sleep before next check
                threading.Event().wait(1.0)
                
            except Exception as e:
                print(f"⚠️  Approval monitoring error: {e}")
    
    def update_user_preferences(self, preferences: Dict[str, Any]):
        """Update user preferences."""
        
        if "risk_tolerance" in preferences:
            self.user_preferences.risk_tolerance = RiskLevel(preferences["risk_tolerance"])
        
        if "auto_approve_low_risk" in preferences:
            self.user_preferences.auto_approve_low_risk = preferences["auto_approve_low_risk"]
        
        if "auto_approve_threshold" in preferences:
            self.user_preferences.auto_approve_threshold = preferences["auto_approve_threshold"]
        
        if "trusted_protocols" in preferences:
            self.user_preferences.trusted_protocols = preferences["trusted_protocols"]
        
        print(f"✅ User preferences updated")
    
    def get_approval_statistics(self) -> Dict[str, Any]:
        """Get approval system statistics."""
        
        total_requests = len(self.approval_history)
        if total_requests == 0:
            return {"total_requests": 0}
        
        approved = len([r for r in self.approval_history if r.status == ApprovalStatus.APPROVED])
        denied = len([r for r in self.approval_history if r.status == ApprovalStatus.DENIED])
        timeout = len([r for r in self.approval_history if r.status == ApprovalStatus.TIMEOUT])
        auto_approved = len([r for r in self.approval_history if r.auto_approve_eligible])
        
        return {
            "total_requests": total_requests,
            "approved": approved,
            "denied": denied,
            "timeout": timeout,
            "auto_approved": auto_approved,
            "approval_rate": approved / total_requests,
            "auto_approval_rate": auto_approved / total_requests,
            "pending_requests": len(self.pending_requests),
            "average_response_time": self._calculate_average_response_time()
        }
    
    def _calculate_average_response_time(self) -> float:
        """Calculate average response time for completed requests."""
        
        completed_requests = [r for r in self.approval_history if r.status != ApprovalStatus.TIMEOUT]
        if not completed_requests:
            return 0.0
        
        # Placeholder calculation - would track actual response times
        return 45.0  # seconds
    
    def shutdown(self):
        """Shutdown the approval system."""
        self.monitoring_active = False
        if self.monitor_thread.is_alive():
            self.monitor_thread.join(timeout=1.0)

# Convenience functions for common approval scenarios

async def request_transaction_approval(amount: float, protocol: str, operation: str) -> ApprovalRequest:
    """Request approval for a transaction."""
    
    system = HumanInTheLoopSystem()
    
    operation_data = {
        "type": "transaction",
        "description": f"{operation} ${amount:,.2f} on {protocol}",
        "value": amount,
        "protocol": protocol,
        "impact": f"Financial transaction of ${amount:,.2f}",
        "irreversible": True
    }
    
    return await system.request_approval(operation_data)

async def request_tool_usage_approval(tool_name: str, risk_level: str) -> ApprovalRequest:
    """Request approval for experimental tool usage."""
    
    system = HumanInTheLoopSystem()
    
    operation_data = {
        "type": "tool_usage",
        "description": f"Use experimental tool: {tool_name}",
        "experimental_tool": True,
        "impact": "May interact with unverified smart contracts",
        "risk_level": risk_level
    }
    
    return await system.request_approval(operation_data)
