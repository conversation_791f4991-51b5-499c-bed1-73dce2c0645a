#!/usr/bin/env python3
"""
Self-Healing CLI interface for the AP3X Crypto Agent
Enhanced CLI with Context7 optimizations, real-time streaming, and advanced monitoring.
"""

import asyncio
import argparse
import sys
import os
import json
from typing import Optional
from datetime import datetime
from dotenv import load_dotenv

from enhanced_agent_healing import create_self_healing_agent, SelfHealingEnhancedAgent
from langchain_core.runnables import RunnableConfig

# Load environment variables
load_dotenv()

class Colors:
    """Enhanced ANSI color codes for terminal output."""
    BLUE = '\033[94m'
    GREEN = '\033[92m'
    YELLOW = '\033[93m'
    RED = '\033[91m'
    PURPLE = '\033[95m'
    CYAN = '\033[96m'
    WHITE = '\033[97m'
    BOLD = '\033[1m'
    DIM = '\033[2m'
    UNDERLINE = '\033[4m'
    BLINK = '\033[5m'
    END = '\033[0m'

def print_banner():
    """Print the enhanced self-healing CLI banner."""
    banner = f"""
{Colors.CYAN}{Colors.BOLD}
╔══════════════════════════════════════════════════════════════╗
║        🚀 Self-Healing AP3X Crypto Agent (Context7)        ║
║     Advanced LangGraph + MCP + Multi-Agent Architecture     ║
║       Real-time Streaming • Persistent Memory • AI Ops      ║
╚══════════════════════════════════════════════════════════════╝
{Colors.END}
"""
    print(banner)

def print_health_status(health_data):
    """Print comprehensive health status."""
    print(f"\n{Colors.PURPLE}🏥 System Health Status:{Colors.END}")
    
    overall_status = health_data.get("overall_status", "unknown")
    status_color = Colors.GREEN if overall_status == "healthy" else Colors.YELLOW
    print(f"  {status_color}Overall Status: {overall_status.upper()}{Colors.END}")
    
    print(f"  {Colors.BLUE}Components: {health_data.get('healthy_components', 0)}/{health_data.get('total_components', 0)} healthy{Colors.END}")
    print(f"  {Colors.YELLOW}Recent Failures: {health_data.get('recent_failures', 0)}{Colors.END}")
    
    # Circuit breaker status
    circuit_breakers = health_data.get("circuit_breakers", {})
    if circuit_breakers:
        print(f"  {Colors.CYAN}Circuit Breakers:{Colors.END}")
        for name, state in circuit_breakers.items():
            state_color = Colors.GREEN if state == "CLOSED" else Colors.RED
            print(f"    {state_color}{name}: {state}{Colors.END}")
    
    # Component details
    components = health_data.get("components", {})
    if components:
        print(f"  {Colors.CYAN}Component Details:{Colors.END}")
        for name, details in components.items():
            status = details.get("status", "unknown")
            response_time = details.get("response_time", 0)
            error_rate = details.get("error_rate", 0) * 100
            
            status_color = Colors.GREEN if status == "healthy" else Colors.RED
            print(f"    {status_color}{name}: {status} ({response_time:.2f}s, {error_rate:.1f}% errors){Colors.END}")

def print_recovery_events(events):
    """Print recent recovery events."""
    if not events:
        return
        
    print(f"\n{Colors.PURPLE}🔧 Recent Recovery Events:{Colors.END}")
    for event in events[-5:]:  # Show last 5 events
        timestamp = event.get("timestamp", "")
        component = event.get("component", "")
        failure_type = event.get("failure_type", "")
        recovery_time = event.get("recovery_time", 0)
        success = event.get("success", False)
        
        success_color = Colors.GREEN if success else Colors.RED
        success_text = "SUCCESS" if success else "FAILED"
        
        print(f"  {success_color}[{timestamp[:19]}] {component} - {failure_type} - {success_text} ({recovery_time:.2f}s){Colors.END}")

async def interactive_mode(agent: SelfHealingEnhancedAgent):
    """Run the enhanced self-healing agent in interactive chat mode with Context7 features."""
    print(f"\n{Colors.GREEN}💬 Enhanced Interactive Mode with Context7 Optimizations{Colors.END}")
    print(f"{Colors.CYAN}Features: Real-time streaming • Persistent memory • Multi-agent reasoning • Advanced self-healing{Colors.END}")
    print(f"{Colors.YELLOW}Commands: 'health', 'tools', 'profile <fast|balanced|thorough>', 'memory', 'help', 'quit'{Colors.END}")

    # Generate unique session ID for persistent memory
    session_id = f"session_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
    execution_profile = "balanced"  # Default profile

    config = RunnableConfig(
        recursion_limit=50,
        configurable={
            "thread_id": session_id,
            "user_id": "cli_user",
            "execution_profile": execution_profile
        }
    )

    print(f"{Colors.DIM}Session ID: {session_id} | Profile: {execution_profile}{Colors.END}")
    
    while True:
        try:
            # Get user input
            user_input = input(f"\n{Colors.BLUE}You:{Colors.END} ").strip()
            
            if not user_input:
                continue
                
            # Handle enhanced commands
            if user_input.lower() in ['quit', 'exit', 'q']:
                print(f"{Colors.YELLOW}👋 Goodbye!{Colors.END}")
                break
            elif user_input.lower() in ['help', 'h']:
                print_enhanced_help()
                continue
            elif user_input.lower() == 'health':
                health_status = agent.get_health_status()
                print_health_status(health_status)
                recovery_events = agent.get_recent_recovery_events()
                print_recovery_events(recovery_events)
                continue
            elif user_input.lower() == 'tools':
                print_available_tools(agent)
                continue
            elif user_input.lower().startswith('profile '):
                new_profile = user_input[8:].strip().lower()
                if new_profile in ['fast', 'balanced', 'thorough']:
                    execution_profile = new_profile
                    config = RunnableConfig(
                        recursion_limit=50,
                        configurable={
                            "thread_id": session_id,
                            "user_id": "cli_user",
                            "execution_profile": execution_profile
                        }
                    )
                    print(f"{Colors.GREEN}✅ Execution profile set to: {execution_profile}{Colors.END}")
                else:
                    print(f"{Colors.RED}❌ Invalid profile. Use: fast, balanced, or thorough{Colors.END}")
                continue
            elif user_input.lower() == 'memory':
                print_memory_status(agent, session_id)
                continue
            elif user_input.lower().startswith('recover '):
                component = user_input[8:].strip()
                print(f"{Colors.YELLOW}Triggering recovery for {component}...{Colors.END}")
                success = await agent.trigger_recovery(component)
                if success:
                    print(f"{Colors.GREEN}✅ Recovery successful for {component}{Colors.END}")
                else:
                    print(f"{Colors.RED}❌ Recovery failed for {component}{Colors.END}")
                continue
            elif user_input.lower() == 'check':
                print(f"{Colors.YELLOW}Forcing health check...{Colors.END}")
                health_status = await agent.force_health_check()
                print_health_status(health_status)
                continue
            elif user_input.lower() in ['clear', 'cls']:
                os.system('cls' if os.name == 'nt' else 'clear')
                print_banner()
                continue
            
            # Process the query with enhanced streaming and self-healing
            print(f"{Colors.PURPLE}Agent:{Colors.END} {Colors.DIM}Processing with Context7 optimizations and real-time streaming...{Colors.END}")

            try:
                start_time = datetime.now()
                response_parts = []

                # Check if streaming is available
                if hasattr(agent, 'stream'):
                    # Stream the response in real-time
                    async for chunk in agent.stream(user_input, config):
                        # Handle different types of streaming chunks
                        if "final_response" in chunk:
                            response = chunk["final_response"]
                            if response and response not in response_parts:
                                response_parts.append(response)
                                print(f"\n{Colors.GREEN}Response:{Colors.END} {response}")

                        # Show planning progress
                        if "planning" in chunk:
                            planning = chunk["planning"]
                            if "execution_plan" in planning and planning["execution_plan"]:
                                plan_steps = planning["execution_plan"]
                                print(f"{Colors.CYAN}📋 Plan: {' → '.join(plan_steps[:3])}{'...' if len(plan_steps) > 3 else ''}{Colors.END}")

                        # Show tool execution
                        if "execution" in chunk:
                            execution = chunk["execution"]
                            tools_used = execution.get("tools_executed", [])
                            if tools_used:
                                print(f"{Colors.YELLOW}🔧 Tools: {', '.join(tools_used[-3:])}{Colors.END}")

                        # Show agent mode routing
                        if "agent_mode" in chunk:
                            mode = chunk["agent_mode"]
                            print(f"{Colors.PURPLE}🎯 Mode: {mode}{Colors.END}")
                else:
                    # Fallback to regular invoke
                    result = await agent.invoke(user_input, config)
                    final_response = result.get("final_response", "No response generated")
                    print(f"\n{Colors.GREEN}Response:{Colors.END} {final_response}")
                    response_parts = [final_response]

                # Show execution summary
                end_time = datetime.now()
                execution_time = (end_time - start_time).total_seconds()
                print(f"\n{Colors.DIM}⏱️  Total execution time: {execution_time:.2f}s | Profile: {execution_profile}{Colors.END}")

                # Show healing metadata if available
                if hasattr(agent, 'get_health_status'):
                    health_status = agent.get_health_status()
                    if health_status.get("overall_status") != "healthy":
                        print(f"{Colors.YELLOW}🏥 Health: {health_status.get('overall_status', 'unknown')}{Colors.END}")

            except Exception as e:
                print(f"{Colors.RED}❌ Error: {e}{Colors.END}")
                print(f"{Colors.YELLOW}💡 The enhanced self-healing system is working to resolve this issue.{Colors.END}")
                
        except KeyboardInterrupt:
            print(f"\n{Colors.YELLOW}👋 Interrupted by user{Colors.END}")
            break
        except Exception as e:
            print(f"{Colors.RED}❌ Unexpected error: {e}{Colors.END}")

async def enhanced_single_query_mode(agent: SelfHealingEnhancedAgent, query: str, profile: str = "balanced", stream: bool = False) -> bool:
    """Process a single query with enhanced features and return success status."""
    try:
        print(f"{Colors.PURPLE}Processing:{Colors.END} {query}")
        print(f"{Colors.DIM}Profile: {profile} | Streaming: {'enabled' if stream else 'disabled'}{Colors.END}")

        config = RunnableConfig(
            recursion_limit=50,
            configurable={
                "thread_id": f"single_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
                "user_id": "cli_user",
                "execution_profile": profile
            }
        )

        start_time = datetime.now()

        if stream and hasattr(agent, 'stream'):
            # Stream the response
            response_parts = []
            async for chunk in agent.stream(query, config):
                if "final_response" in chunk:
                    response = chunk["final_response"]
                    if response and response not in response_parts:
                        response_parts.append(response)
                        print(f"\n{Colors.GREEN}Response:{Colors.END} {response}")

                # Show progress indicators
                if "planning" in chunk:
                    planning = chunk["planning"]
                    if "execution_plan" in planning and planning["execution_plan"]:
                        plan_steps = planning["execution_plan"]
                        print(f"{Colors.CYAN}📋 Plan: {' → '.join(plan_steps[:3])}{Colors.END}")

                if "execution" in chunk:
                    execution = chunk["execution"]
                    tools_used = execution.get("tools_executed", [])
                    if tools_used:
                        print(f"{Colors.YELLOW}🔧 Tools: {', '.join(tools_used[-3:])}{Colors.END}")
        else:
            # Regular invoke
            result = await agent.invoke(query, config)

            # Display the response
            final_response = result.get("final_response", "No response generated")
            print(f"\n{Colors.GREEN}Response:{Colors.END} {final_response}")

            # Display enhanced metadata
            healing_metadata = result.get("healing_metadata", {})
            if healing_metadata:
                execution_time = healing_metadata.get("execution_time", 0)
                health_status = healing_metadata.get("health_status", {})
                recovery_events = healing_metadata.get("recovery_events", [])
                graph_type = healing_metadata.get("graph_type", "unknown")
                tool_performance = healing_metadata.get("tool_performance", {})

                print(f"\n{Colors.CYAN}🔧 Enhanced Execution Report:{Colors.END}")
                print(f"  Execution Time: {execution_time:.2f}s")
                print(f"  Graph Type: {graph_type}")
                print(f"  System Health: {health_status.get('overall_status', 'unknown')}")
                print(f"  Recovery Events: {len(recovery_events)}")

                if tool_performance:
                    print(f"  Tool Categories: {len(tool_performance)}")

                if recovery_events:
                    print(f"  {Colors.YELLOW}Recovery actions were taken during execution{Colors.END}")

        # Show final timing
        end_time = datetime.now()
        total_time = (end_time - start_time).total_seconds()
        print(f"\n{Colors.DIM}⏱️  Total execution time: {total_time:.2f}s{Colors.END}")

        return True

    except Exception as e:
        print(f"{Colors.RED}❌ Error: {e}{Colors.END}")
        return False

async def single_query_mode(agent: SelfHealingEnhancedAgent, query: str) -> bool:
    """Legacy single query mode for backward compatibility."""
    return await enhanced_single_query_mode(agent, query)

def print_enhanced_help():
    """Print detailed help information with Context7 features."""
    help_text = f"""
{Colors.CYAN}{Colors.BOLD}Enhanced Self-Healing AP3X Crypto Agent - Help{Colors.END}

{Colors.YELLOW}🚀 Context7 Enhanced Features:{Colors.END}
  {Colors.GREEN}Real-time streaming{Colors.END}     - See responses as they're generated
  {Colors.GREEN}Persistent memory{Colors.END}       - Conversations saved across sessions
  {Colors.GREEN}Multi-agent reasoning{Colors.END}   - Specialist agents for different domains
  {Colors.GREEN}Dynamic tool selection{Colors.END}  - Intelligent tool routing and execution
  {Colors.GREEN}Execution profiles{Colors.END}      - Fast, balanced, or thorough processing

{Colors.YELLOW}🔧 System Commands:{Colors.END}
  {Colors.GREEN}health{Colors.END}                   - Show comprehensive system health status
  {Colors.GREEN}tools{Colors.END}                    - Show available tools by category
  {Colors.GREEN}memory{Colors.END}                   - Show conversation memory status
  {Colors.GREEN}profile <fast|balanced|thorough>{Colors.END} - Set execution profile
  {Colors.GREEN}check{Colors.END}                    - Force health check of all components
  {Colors.GREEN}recover <component>{Colors.END}      - Manually trigger recovery for a component

{Colors.YELLOW}💬 Chat Commands:{Colors.END}
  {Colors.GREEN}help{Colors.END}                     - Show this help message
  {Colors.GREEN}clear{Colors.END}                    - Clear screen and show banner
  {Colors.GREEN}quit{Colors.END}                     - Exit the application

{Colors.YELLOW}🎯 Example Queries:{Colors.END}
  • "Analyze wallet 0x742d35Cc... and provide investment insights"
  • "Search for latest Bitcoin news and analyze market sentiment"
  • "Get NFT collection data for CryptoPunks and compare with BAYC"
  • "Analyze DeFi protocol TVL trends and suggest yield opportunities"

{Colors.YELLOW}🤖 Specialist Agents:{Colors.END}
  • Crypto Specialist - Blockchain analysis, token research, wallet investigations
  • Search Specialist - Web research, news analysis, market intelligence

{Colors.YELLOW}⚡ Execution Profiles:{Colors.END}
  • {Colors.GREEN}Fast{Colors.END} - Quick responses, limited tools (3 tools, 5 steps, 30s timeout)
  • {Colors.YELLOW}Balanced{Colors.END} - Good balance of speed and thoroughness (7 tools, 10 steps, 60s timeout)
  • {Colors.RED}Thorough{Colors.END} - Comprehensive analysis (15 tools, 20 steps, 120s timeout)

{Colors.YELLOW}🏥 Self-Healing Features:{Colors.END}
  • Automatic failure detection and recovery
  • Circuit breakers for external services
  • Adaptive fallback systems
  • Real-time health monitoring
  • Performance optimization
  • Graceful degradation

{Colors.CYAN}The enhanced agent uses Context7 best practices for superior performance and reliability.{Colors.END}
"""
    print(help_text)

def print_available_tools(agent: SelfHealingEnhancedAgent):
    """Print available tools organized by category."""
    try:
        if hasattr(agent, 'tool_registry') and agent.tool_registry:
            print(f"\n{Colors.CYAN}🛠️  Available Tools by Category:{Colors.END}")

            for category, tools in agent.tool_registry.items():
                print(f"  {Colors.YELLOW}{category.title()} ({len(tools)} tools):{Colors.END}")
                for tool in tools[:5]:  # Show first 5 tools
                    print(f"    • {tool.name if hasattr(tool, 'name') else str(tool)}")
                if len(tools) > 5:
                    print(f"    • ... and {len(tools) - 5} more")
        else:
            # Fallback to original method
            tools = agent.list_tools() if hasattr(agent, 'list_tools') else []
            print(f"\n{Colors.CYAN}🛠️  Available Tools ({len(tools)} total):{Colors.END}")
            for tool in tools[:10]:
                print(f"  • {tool}")
            if len(tools) > 10:
                print(f"  • ... and {len(tools) - 10} more")

        # Show specialist agents
        if hasattr(agent, 'specialist_agents') and agent.specialist_agents:
            print(f"\n{Colors.PURPLE}🤖 Specialist Agents:{Colors.END}")
            for name in agent.specialist_agents.keys():
                print(f"  • {name}_specialist")

    except Exception as e:
        print(f"{Colors.RED}❌ Error retrieving tools: {e}{Colors.END}")

def print_memory_status(agent: SelfHealingEnhancedAgent, session_id: str):
    """Print memory and conversation status."""
    try:
        print(f"\n{Colors.PURPLE}🧠 Memory Status:{Colors.END}")
        print(f"  {Colors.CYAN}Session ID: {session_id}{Colors.END}")

        # Check if enhanced memory features are available
        if hasattr(agent, 'checkpointer') and agent.checkpointer:
            checkpointer_type = type(agent.checkpointer).__name__
            print(f"  {Colors.GREEN}Checkpointer: {checkpointer_type}{Colors.END}")
        else:
            print(f"  {Colors.YELLOW}Checkpointer: Not available{Colors.END}")

        if hasattr(agent, 'store') and agent.store:
            store_type = type(agent.store).__name__
            print(f"  {Colors.GREEN}Store: {store_type}{Colors.END}")
        else:
            print(f"  {Colors.YELLOW}Store: Not available{Colors.END}")

        # Show conversation history if available
        if hasattr(agent, 'get_conversation_history'):
            try:
                # This would need to be implemented in the agent
                print(f"  {Colors.DIM}Conversation history available{Colors.END}")
            except:
                print(f"  {Colors.DIM}Conversation history not accessible{Colors.END}")

    except Exception as e:
        print(f"{Colors.RED}❌ Error retrieving memory status: {e}{Colors.END}")

def print_tools(agent: SelfHealingEnhancedAgent):
    """Print available tools with health status."""
    tools = agent.list_tools()
    health_status = agent.get_health_status()
    
    print(f"\n{Colors.CYAN}🛠️  Available Tools ({len(tools)} total):{Colors.END}")
    
    # Group tools by category
    categories = {
        "Utility": [t for t in tools if any(name in t for name in ["add", "multiply", "text", "hash"])],
        "Search": [t for t in tools if any(name in t for name in ["search", "tavily", "duckduckgo"])],
        "Blockchain": [t for t in tools if any(name in t for name in ["evm_", "solana_", "moralis"])],
        "Thinking": [t for t in tools if "thinking" in t]
    }
    
    for category, category_tools in categories.items():
        if category_tools:
            # Check health status for this category
            category_key = f"{category.lower()}_tools"
            component_health = health_status.get("components", {}).get(category_key, {})
            status = component_health.get("status", "unknown")
            
            status_color = Colors.GREEN if status == "healthy" else Colors.RED
            print(f"  {status_color}{category} ({len(category_tools)} tools) - {status}{Colors.END}")
            
            # Show first few tools
            for tool in category_tools[:3]:
                print(f"    • {tool}")
            if len(category_tools) > 3:
                print(f"    • ... and {len(category_tools) - 3} more")

async def main():
    """Enhanced main CLI entry point with Context7 features."""
    parser = argparse.ArgumentParser(description="Enhanced Self-Healing AP3X Crypto Agent CLI with Context7")
    parser.add_argument("--query", "-q", help="Single query mode")
    parser.add_argument("--tools", "-t", action="store_true", help="List available tools with health status")
    parser.add_argument("--health", action="store_true", help="Show health status and exit")
    parser.add_argument("--profile", "-p", choices=["fast", "balanced", "thorough"],
                       default="balanced", help="Execution profile (default: balanced)")
    parser.add_argument("--memory", "-m", action="store_true", help="Enable persistent memory")
    parser.add_argument("--backend", "-b", choices=["memory", "redis"],
                       default="memory", help="Memory backend (default: memory)")
    parser.add_argument("--stream", "-s", action="store_true", help="Enable streaming mode")

    args = parser.parse_args()

    # Print enhanced banner
    print_banner()

    # Initialize enhanced self-healing agent
    print(f"{Colors.CYAN}🚀 Initializing Enhanced Self-Healing Agent with Context7 optimizations...{Colors.END}")

    if args.memory:
        print(f"{Colors.YELLOW}📚 Persistent memory enabled with {args.backend} backend{Colors.END}")

    agent = None
    try:
        agent = await create_self_healing_agent(
            use_persistent_memory=args.memory,
            memory_backend=args.backend
        )
        print(f"{Colors.GREEN}✅ Enhanced self-healing agent initialized successfully{Colors.END}")

        # Show initialization summary
        if hasattr(agent, 'tool_registry') and agent.tool_registry:
            total_tools = sum(len(tools) for tools in agent.tool_registry.values())
            print(f"{Colors.CYAN}🛠️  {total_tools} tools loaded across {len(agent.tool_registry)} categories{Colors.END}")

        if hasattr(agent, 'specialist_agents') and agent.specialist_agents:
            print(f"{Colors.PURPLE}🤖 {len(agent.specialist_agents)} specialist agents available{Colors.END}")

        # Handle different modes
        if args.health:
            health_status = agent.get_health_status()
            print_health_status(health_status)
            recovery_events = agent.get_recent_recovery_events()
            print_recovery_events(recovery_events)
            return 0

        elif args.tools:
            print_available_tools(agent)
            return 0

        elif args.query:
            success = await enhanced_single_query_mode(agent, args.query, args.profile, args.stream)
            return 0 if success else 1

        else:
            # Default to enhanced interactive mode
            await interactive_mode(agent)
            return 0
            
    except KeyboardInterrupt:
        print(f"\n{Colors.YELLOW}👋 Interrupted by user{Colors.END}")
        return 0
    except Exception as e:
        print(f"{Colors.RED}❌ Error initializing self-healing agent: {e}{Colors.END}")
        return 1
    finally:
        if agent:
            await agent.cleanup()

if __name__ == "__main__":
    sys.exit(asyncio.run(main()))
