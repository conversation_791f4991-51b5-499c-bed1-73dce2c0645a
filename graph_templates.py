#!/usr/bin/env python3
"""
Graph Template System for Dynamic Graph Recompilation
Manages different graph variants and enables runtime graph switching.
"""

import json
import hashlib
from typing import Dict, Any, List, Optional, Callable
from dataclasses import dataclass, field, asdict
from datetime import datetime
from enum import Enum

from langgraph.graph import StateGraph, END, START
from enhanced_agent import AgentState

class GraphType(Enum):
    """Types of graph optimizations."""
    BASE = "base"
    FAST_TRACK = "fast_track"
    SPECIALIST_DIRECT = "specialist_direct"
    TOOL_CHAIN = "tool_chain"
    CACHED_PATH = "cached_path"
    EXPERIMENTAL = "experimental"

@dataclass
class GraphTemplate:
    """Template for storing graph configurations."""
    template_id: str
    graph_type: GraphType
    name: str
    description: str
    node_definitions: Dict[str, str]  # node_name -> method_name
    edge_definitions: List[Dict[str, Any]]  # edge configurations
    conditional_edges: List[Dict[str, Any]]  # conditional edge configurations
    entry_point: str
    performance_metrics: Dict[str, float] = field(default_factory=dict)
    created_at: datetime = field(default_factory=datetime.now)
    last_used: Optional[datetime] = None
    usage_count: int = 0
    success_rate: float = 0.0
    average_execution_time: float = 0.0
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for serialization."""
        data = asdict(self)
        data['graph_type'] = self.graph_type.value
        data['created_at'] = self.created_at.isoformat()
        data['last_used'] = self.last_used.isoformat() if self.last_used else None
        return data
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'GraphTemplate':
        """Create from dictionary."""
        data['graph_type'] = GraphType(data['graph_type'])
        data['created_at'] = datetime.fromisoformat(data['created_at'])
        if data['last_used']:
            data['last_used'] = datetime.fromisoformat(data['last_used'])
        return cls(**data)

class GraphTemplateManager:
    """Manages graph templates and enables dynamic graph switching."""
    
    def __init__(self):
        self.templates: Dict[str, GraphTemplate] = {}
        self.active_template_id: str = "base"
        self.template_history: List[Dict[str, Any]] = []
        
        # Initialize with base template
        self._create_base_template()
    
    def _create_base_template(self):
        """Create the base graph template."""
        base_template = GraphTemplate(
            template_id="base",
            graph_type=GraphType.BASE,
            name="Base Enhanced Agent",
            description="Original enhanced agent graph with all capabilities",
            node_definitions={
                "router": "_query_router_node",
                "direct_executor": "_direct_execution_node",
                "dynamic_planner": "_dynamic_planning_node",
                "tools": "_enhanced_tool_node",
                "reflector": "_reflector_node",
                "address_specialist": "_process_with_react_agent_node",
                "synthesizer": "_synthesis_node",
                "swarm_coordinator": "_swarm_coordination_node"
            },
            edge_definitions=[
                {"from": "direct_executor", "to": END},
                {"from": "address_specialist", "to": END},
                {"from": "swarm_coordinator", "to": END},
                {"from": "dynamic_planner", "to": "tools"},
                {"from": "tools", "to": "reflector"},
                {"from": "synthesizer", "to": END}
            ],
            conditional_edges=[
                {
                    "from": "router",
                    "condition": "next_action",
                    "mapping": {
                        "direct_execute": "direct_executor",
                        "plan_and_reflect": "dynamic_planner",
                        "delegate_to_specialist": "address_specialist",
                        "delegate_to_swarm": "swarm_coordinator"
                    }
                },
                {
                    "from": "reflector",
                    "condition": "last_tool_output_quality",
                    "mapping": {
                        "complete": "synthesizer",
                        "default": "dynamic_planner"
                    }
                }
            ],
            entry_point="router"
        )
        
        self.templates["base"] = base_template
    
    def create_fast_track_template(self) -> GraphTemplate:
        """Create a fast-track template for simple queries."""
        fast_track = GraphTemplate(
            template_id="fast_track",
            graph_type=GraphType.FAST_TRACK,
            name="Fast Track Processor",
            description="Direct router to synthesis for simple, high-confidence queries",
            node_definitions={
                "router": "_query_router_node",
                "fast_synthesizer": "_fast_synthesis_node",
                "fallback_planner": "_dynamic_planning_node",
                "tools": "_enhanced_tool_node",
                "synthesizer": "_synthesis_node"
            },
            edge_definitions=[
                {"from": "fast_synthesizer", "to": END},
                {"from": "fallback_planner", "to": "tools"},
                {"from": "tools", "to": "synthesizer"},
                {"from": "synthesizer", "to": END}
            ],
            conditional_edges=[
                {
                    "from": "router",
                    "condition": "confidence_level",
                    "mapping": {
                        "high": "fast_synthesizer",
                        "default": "fallback_planner"
                    }
                }
            ],
            entry_point="router"
        )
        
        return fast_track
    
    def create_specialist_direct_template(self) -> GraphTemplate:
        """Create template for direct specialist routing."""
        specialist_direct = GraphTemplate(
            template_id="specialist_direct",
            graph_type=GraphType.SPECIALIST_DIRECT,
            name="Specialist Direct Router",
            description="Direct routing to specialists for clear domain queries",
            node_definitions={
                "smart_router": "_smart_router_node",
                "crypto_forensics": "_crypto_forensics_direct_node",
                "defi_yield": "_defi_yield_direct_node",
                "nft_market": "_nft_market_direct_node",
                "general_processor": "_dynamic_planning_node",
                "tools": "_enhanced_tool_node",
                "synthesizer": "_synthesis_node"
            },
            edge_definitions=[
                {"from": "crypto_forensics", "to": END},
                {"from": "defi_yield", "to": END},
                {"from": "nft_market", "to": END},
                {"from": "general_processor", "to": "tools"},
                {"from": "tools", "to": "synthesizer"},
                {"from": "synthesizer", "to": END}
            ],
            conditional_edges=[
                {
                    "from": "smart_router",
                    "condition": "specialist_domain",
                    "mapping": {
                        "forensics": "crypto_forensics",
                        "defi": "defi_yield",
                        "nft": "nft_market",
                        "default": "general_processor"
                    }
                }
            ],
            entry_point="smart_router"
        )
        
        return specialist_direct
    
    def create_tool_chain_template(self, tool_sequence: List[str]) -> GraphTemplate:
        """Create template for pre-configured tool chains."""
        chain_id = hashlib.md5("->".join(tool_sequence).encode()).hexdigest()[:8]
        
        tool_chain = GraphTemplate(
            template_id=f"tool_chain_{chain_id}",
            graph_type=GraphType.TOOL_CHAIN,
            name=f"Tool Chain: {' -> '.join(tool_sequence)}",
            description=f"Pre-configured tool sequence for common pattern",
            node_definitions={
                "router": "_query_router_node",
                "tool_chain_executor": "_tool_chain_executor_node",
                "synthesizer": "_synthesis_node"
            },
            edge_definitions=[
                {"from": "router", "to": "tool_chain_executor"},
                {"from": "tool_chain_executor", "to": "synthesizer"},
                {"from": "synthesizer", "to": END}
            ],
            conditional_edges=[],
            entry_point="router",
            performance_metrics={"tool_sequence": tool_sequence}
        )
        
        return tool_chain
    
    def register_template(self, template: GraphTemplate):
        """Register a new graph template."""
        self.templates[template.template_id] = template
        
        self.template_history.append({
            "action": "template_registered",
            "template_id": template.template_id,
            "timestamp": datetime.now().isoformat(),
            "description": template.description
        })
    
    def get_template(self, template_id: str) -> Optional[GraphTemplate]:
        """Get a graph template by ID."""
        return self.templates.get(template_id)
    
    def get_best_template_for_query(self, intent: str, complexity: str, confidence: float) -> GraphTemplate:
        """Select the best template for a given query."""
        
        # Fast track for high-confidence simple queries
        if confidence > 0.9 and complexity == "simple":
            fast_track = self.get_template("fast_track")
            if fast_track and fast_track.success_rate > 0.8:
                return fast_track
        
        # Specialist direct for clear domain queries
        specialist_keywords = {
            "forensics": ["trace", "investigate", "security", "exploit"],
            "defi": ["yield", "farming", "liquidity", "apy"],
            "nft": ["nft", "collection", "floor", "opensea"]
        }
        
        for domain, keywords in specialist_keywords.items():
            if any(keyword in intent.lower() for keyword in keywords):
                specialist_template = self.get_template("specialist_direct")
                if specialist_template and specialist_template.success_rate > 0.7:
                    return specialist_template
        
        # Default to base template
        return self.templates["base"]
    
    def update_template_performance(self, template_id: str, execution_time: float, success: bool):
        """Update performance metrics for a template."""
        template = self.templates.get(template_id)
        if not template:
            return
        
        template.usage_count += 1
        template.last_used = datetime.now()
        
        # Update success rate
        if template.usage_count == 1:
            template.success_rate = 1.0 if success else 0.0
        else:
            # Exponential moving average
            alpha = 0.1
            template.success_rate = (alpha * (1.0 if success else 0.0) + 
                                   (1 - alpha) * template.success_rate)
        
        # Update average execution time
        if template.usage_count == 1:
            template.average_execution_time = execution_time
        else:
            alpha = 0.1
            template.average_execution_time = (alpha * execution_time + 
                                             (1 - alpha) * template.average_execution_time)
    
    def get_template_rankings(self) -> List[Dict[str, Any]]:
        """Get templates ranked by performance."""
        rankings = []
        
        for template in self.templates.values():
            if template.usage_count > 0:
                # Combined score: success_rate * speed_factor
                speed_factor = max(0.1, 1.0 / (template.average_execution_time + 1.0))
                score = template.success_rate * speed_factor
                
                rankings.append({
                    "template_id": template.template_id,
                    "name": template.name,
                    "score": score,
                    "success_rate": template.success_rate,
                    "avg_time": template.average_execution_time,
                    "usage_count": template.usage_count
                })
        
        return sorted(rankings, key=lambda x: x["score"], reverse=True)
    
    def cleanup_unused_templates(self, min_usage: int = 5, max_age_days: int = 30):
        """Remove templates that are unused or performing poorly."""
        cutoff_date = datetime.now() - timedelta(days=max_age_days)
        
        templates_to_remove = []
        for template_id, template in self.templates.items():
            if template_id == "base":  # Never remove base template
                continue
                
            if (template.usage_count < min_usage and 
                template.created_at < cutoff_date):
                templates_to_remove.append(template_id)
        
        for template_id in templates_to_remove:
            del self.templates[template_id]
            self.template_history.append({
                "action": "template_removed",
                "template_id": template_id,
                "timestamp": datetime.now().isoformat(),
                "reason": "unused_or_old"
            })
    
    def export_templates(self) -> Dict[str, Any]:
        """Export all templates for backup/analysis."""
        return {
            "templates": {tid: template.to_dict() for tid, template in self.templates.items()},
            "active_template": self.active_template_id,
            "history": self.template_history
        }
