#!/usr/bin/env python3
"""
Self-Healing Integration for Enhanced LangGraph MCP Agent
Integrates advanced self-healing mechanisms with Context7 best practices.
Enhanced with sophisticated state management, multi-agent patterns, and advanced MCP integration.
"""

import asyncio
import time
import json
import logging
import uuid
from typing import Dict, Any, List, Optional, Callable, Literal, Annotated, Union
from datetime import datetime, timed<PERSON>ta
from typing_extensions import TypedDict, NotRequired

from self_healing import (
    SelfHealingAgent, HealthStatus, FailureType, RecoveryAction,
    HealthMetrics, CircuitBreaker
)
from enhanced_agent import EnhancedLangGraphMCPAgent, AgentState
from mcp_config import get_safe_mcp_configs, get_mcp_server_configs
from langchain_core.messages import HumanMessage, AIMessage, ToolMessage, BaseMessage
from langchain_core.runnables import RunnableConfig
from langchain_mcp_adapters.client import MultiServerMCPClient
from langgraph.graph import StateGraph, MessagesState, START, END
from langgraph.graph.message import add_messages
from langgraph.prebuilt import ToolNode, create_react_agent, InjectedState
from langgraph.types import Command
from langgraph.checkpoint.memory import MemorySaver
from langgraph.store.memory import InMemoryStore

# Enhanced State Schemas following Context7 best practices

class InputState(TypedDict):
    """Clean input schema for external API"""
    query: str
    user_id: NotRequired[str]
    session_id: NotRequired[str]
    context: NotRequired[Dict[str, Any]]
    execution_profile: NotRequired[Literal["fast", "balanced", "thorough"]]

class OutputState(TypedDict):
    """Clean output schema for external API"""
    final_response: str
    reasoning_steps: NotRequired[List[str]]
    tools_used: NotRequired[List[str]]
    execution_time: NotRequired[float]
    confidence_score: NotRequired[float]
    healing_metadata: NotRequired[Dict[str, Any]]

class PlanningState(TypedDict):
    """Advanced planning and reasoning state"""
    execution_plan: List[str]
    current_step: int
    reasoning_history: List[str]
    tool_selection_rationale: List[str]
    plan_confidence: float
    adaptive_adjustments: List[str]

class ExecutionState(TypedDict):
    """Comprehensive tool execution tracking"""
    tools_executed: List[str]
    tool_results: Dict[str, Any]
    execution_errors: List[str]
    performance_metrics: Dict[str, float]
    circuit_breaker_states: Dict[str, str]
    recovery_events: List[Dict[str, Any]]

class MemoryState(TypedDict):
    """Enhanced memory and context management"""
    conversation_summary: str
    key_insights: List[str]
    user_preferences: Dict[str, Any]
    domain_expertise: Dict[str, Any]
    learning_feedback: List[str]

class EnhancedAgentState(InputState, OutputState):
    """Comprehensive agent state combining all schemas with Context7 patterns"""
    messages: Annotated[List[BaseMessage], add_messages]
    planning: NotRequired[PlanningState]
    execution: NotRequired[ExecutionState]
    memory: NotRequired[MemoryState]
    agent_mode: NotRequired[Literal["crypto", "nft", "defi", "search", "general"]]
    specialist_routing: NotRequired[Dict[str, Any]]
    healing_context: NotRequired[Dict[str, Any]]

class SelfHealingEnhancedAgent(EnhancedLangGraphMCPAgent):
    """
    Enhanced agent with integrated self-healing capabilities and Context7 best practices.
    Features:
    - Sophisticated state management with TypedDict schemas
    - MultiServerMCPClient with advanced patterns
    - Dynamic tool selection and routing
    - Persistent memory with checkpointers
    - Multi-agent patterns for complex reasoning
    - Real-time streaming capabilities
    - Comprehensive health monitoring and recovery
    """

    def __init__(self):
        super().__init__()
        self.healing_agent = SelfHealingAgent(check_interval=30.0)
        self.server_health_checks = {}
        self.tool_circuit_breakers = {}
        self.recovery_attempts = {}
        self.logger = logging.getLogger(__name__)

        # Context7 enhancements
        self.enhanced_mcp_client: Optional[MultiServerMCPClient] = None
        self.tool_registry = {}
        self.specialist_agents = {}
        self.checkpointer = None
        self.store = None
        self.enhanced_graph = None
        self.execution_profiles = {
            "fast": {"max_tools": 3, "max_steps": 5, "timeout": 30},
            "balanced": {"max_tools": 7, "max_steps": 10, "timeout": 60},
            "thorough": {"max_tools": 15, "max_steps": 20, "timeout": 120}
        }
        
    async def initialize(self,
                        server_configs: Dict[str, Dict[str, Any]] = None,
                        use_persistent_memory: bool = False,
                        memory_backend: str = "memory"):
        """
        Initialize the enhanced agent with Context7 best practices and self-healing capabilities.

        Args:
            server_configs: MCP server configurations
            use_persistent_memory: Enable persistent memory with checkpointers
            memory_backend: Memory backend type ("memory", "redis", "postgres")
        """

        # Initialize the healing system first
        await self.healing_agent.initialize()

        # Setup enhanced memory system with Context7 patterns
        await self._setup_enhanced_memory(use_persistent_memory, memory_backend)

        # Setup model with healing protection
        await self._setup_model_with_healing()

        # Default server configuration if none provided
        if server_configs is None:
            server_configs = get_safe_mcp_configs()

        # Setup enhanced MCP client with Context7 patterns
        await self._setup_enhanced_mcp_client(server_configs)

        # Setup dynamic tool system with intelligent routing
        await self._setup_dynamic_tool_system()

        # Setup specialist agents for multi-agent patterns
        await self._setup_specialist_agents()

        # Build enhanced graph with Context7 architecture
        await self._build_enhanced_graph()
        
        # Build the enhanced graph
        self._build_graph()
        
        # Register all components for health monitoring
        await self._register_health_monitoring()
        
        print("✅ Self-Healing Enhanced LangGraph agent with Context7 optimizations created successfully")

    async def _setup_enhanced_memory(self, use_persistent: bool, backend: str):
        """Setup enhanced memory system with Context7 patterns"""
        try:
            if use_persistent and backend == "redis":
                # Try Redis for production-grade persistence
                try:
                    from langgraph.checkpoint.redis import RedisSaver
                    self.checkpointer = RedisSaver.from_conn_string("redis://localhost:6379")
                    self.logger.info("Redis checkpointer initialized")
                except ImportError:
                    self.logger.warning("Redis not available, falling back to memory")
                    self.checkpointer = MemorySaver()
            else:
                # Use memory for development
                self.checkpointer = MemorySaver()

            # Setup store for semantic memory
            self.store = InMemoryStore()

            self.logger.info(f"Enhanced memory system initialized with {backend} backend")

        except Exception as e:
            self.logger.warning(f"Failed to setup enhanced memory, using defaults: {e}")
            self.checkpointer = MemorySaver()
            self.store = InMemoryStore()

    async def _setup_enhanced_mcp_client(self, server_configs: Dict[str, Dict[str, Any]]):
        """Setup enhanced MCP client with Context7 patterns"""
        try:
            # Enhanced MCP configuration with better error handling
            enhanced_configs = {}
            for name, config in server_configs.items():
                # Clean config by removing display-only fields
                clean_config = {k: v for k, v in config.items() if not k.startswith("_")}
                enhanced_configs[name] = {
                    **clean_config,
                    "timeout": clean_config.get("timeout", 30),
                    "retry_attempts": clean_config.get("retry_attempts", 3),
                    "health_check_interval": clean_config.get("health_check_interval", 60)
                }

            self.enhanced_mcp_client = MultiServerMCPClient(enhanced_configs)
            tools = await self.enhanced_mcp_client.get_tools()

            # Create enhanced tool registry with categorization
            for tool in tools:
                category = self._categorize_tool(tool.name)
                if category not in self.tool_registry:
                    self.tool_registry[category] = []
                self.tool_registry[category].append(tool)

            self.logger.info(f"Enhanced MCP client initialized with {len(tools)} tools across {len(self.tool_registry)} categories")

            # Register MCP client for health monitoring
            await self._register_mcp_health_monitoring()

        except Exception as e:
            self.logger.error(f"Failed to setup enhanced MCP client: {e}")
            # Fallback to original setup
            await self._setup_mcp_client_with_healing(server_configs)

    def _categorize_tool(self, tool_name: str) -> str:
        """Categorize tools for intelligent selection"""
        tool_lower = tool_name.lower()

        if any(keyword in tool_lower for keyword in ['evm_', 'solana_', 'wallet', 'token', 'nft', 'defi']):
            return "blockchain"
        elif any(keyword in tool_lower for keyword in ['search', 'tavily', 'web', 'news']):
            return "search"
        elif any(keyword in tool_lower for keyword in ['thinking', 'reasoning', 'sequential']):
            return "reasoning"
        elif any(keyword in tool_lower for keyword in ['add', 'multiply', 'calculate', 'math']):
            return "utility"
        else:
            return "general"

    async def _setup_dynamic_tool_system(self):
        """Setup dynamic tool selection and routing system"""
        # This will be enhanced with ToolNode patterns
        self.tool_performance_metrics = {}
        self.tool_usage_history = {}

        # Initialize performance tracking for each tool category
        for category in self.tool_registry.keys():
            self.tool_performance_metrics[category] = {
                "success_rate": 1.0,
                "avg_response_time": 0.0,
                "error_count": 0,
                "usage_count": 0
            }

        self.logger.info("Dynamic tool system initialized")

    async def _setup_specialist_agents(self):
        """Setup specialist agents for multi-agent patterns"""
        try:
            # Only setup if we have a model available
            if hasattr(self, 'model') and self.model:

                # Crypto Analysis Specialist
                crypto_tools = self.tool_registry.get("blockchain", [])[:10]  # Limit for performance
                if crypto_tools:
                    self.specialist_agents["crypto"] = create_react_agent(
                        model=self.model,
                        tools=crypto_tools,
                        prompt="You are a crypto analysis specialist. Focus on blockchain data, token analysis, wallet investigations, and DeFi protocols.",
                        name="crypto_specialist"
                    )

                # Search and Research Specialist
                search_tools = self.tool_registry.get("search", [])
                if search_tools:
                    self.specialist_agents["search"] = create_react_agent(
                        model=self.model,
                        tools=search_tools,
                        prompt="You are a search and research specialist. Focus on finding relevant information, news analysis, and market intelligence.",
                        name="search_specialist"
                    )

                self.logger.info(f"Initialized {len(self.specialist_agents)} specialist agents")
            else:
                self.logger.warning("Model not available, skipping specialist agent setup")

        except Exception as e:
            self.logger.error(f"Failed to setup specialist agents: {e}")
            self.specialist_agents = {}

    async def _setup_model_with_healing(self):
        """Setup the language model with self-healing protection."""
        try:
            self._setup_model()
            
            # Register model health check
            async def model_health_check():
                try:
                    test_response = await asyncio.wait_for(
                        self.model.ainvoke([HumanMessage(content="test")]),
                        timeout=10.0
                    )
                    return True
                except Exception:
                    return False
                    
            self.healing_agent.register_component(
                "openrouter_model",
                health_check=model_health_check,
                recovery_strategies=[self._recover_model_connection]
            )
            
            # Setup circuit breaker for model calls
            self.model_circuit_breaker = self.healing_agent.get_circuit_breaker(
                "openrouter_model", failure_threshold=3, recovery_timeout=60.0
            )
            
        except Exception as e:
            self.logger.error(f"Failed to setup model with healing: {e}")
            raise
            
    async def _setup_mcp_client_with_healing(self, server_configs: Dict[str, Dict[str, Any]]):
        """Setup MCP client with self-healing protection."""
        from mcp_config import clean_config_for_mcp
        
        # Clean configuration for MCP client
        clean_configs = clean_config_for_mcp(server_configs)
        
        try:
            # Setup MCP client
            self._setup_mcp_client(clean_configs)
            
            # Register health checks for each MCP server
            for server_name in clean_configs.keys():
                await self._register_mcp_server_health(server_name)
                
        except Exception as e:
            self.logger.error(f"Failed to setup MCP client with healing: {e}")
            # Fallback to minimal configuration
            await self._fallback_to_minimal_config()
            
    async def _register_mcp_server_health(self, server_name: str):
        """Register health monitoring for an MCP server."""
        
        async def server_health_check():
            try:
                # Try to get tools from the server as a health check
                if self.client:
                    tools = await asyncio.wait_for(self.client.get_tools(), timeout=10.0)
                    return len(tools) > 0
                return False
            except Exception:
                return False
                
        # Register recovery strategies
        recovery_strategies = [
            lambda comp, ft: self._restart_mcp_server(server_name),
            lambda comp, ft: self._fallback_to_minimal_config()
        ]
        
        self.healing_agent.register_component(
            f"mcp_server_{server_name}",
            health_check=server_health_check,
            recovery_strategies=recovery_strategies
        )
        
    async def _setup_tools_with_healing(self):
        """Setup tools with self-healing protection."""
        try:
            self.tools = await self.client.get_tools()
            print(f"✅ Loaded {len(self.tools)} tools from MCP servers")
            
            # Register health monitoring for tool categories
            await self._register_tool_health_monitoring()
            
            # Create React agent with healing
            self._create_react_agent()
            
        except Exception as e:
            self.logger.error(f"Failed to setup tools: {e}")
            await self._fallback_to_minimal_config()
            
    async def _register_tool_health_monitoring(self):
        """Register health monitoring for different tool categories."""
        
        # Group tools by category
        tool_categories = {
            "utility_tools": [t for t in self.tools if any(name in t.name for name in ["add", "multiply", "text", "hash"])],
            "search_tools": [t for t in self.tools if any(name in t.name for name in ["search", "tavily", "duckduckgo"])],
            "blockchain_tools": [t for t in self.tools if any(name in t.name for name in ["evm_", "solana_", "moralis"])],
            "thinking_tools": [t for t in self.tools if "thinking" in t.name]
        }
        
        for category, tools in tool_categories.items():
            if tools:
                # Create health check for tool category
                async def category_health_check(cat_tools=tools):
                    try:
                        # Test a simple tool from the category
                        if cat_tools:
                            test_tool = cat_tools[0]
                            # This is a basic connectivity test
                            return True
                    except Exception:
                        return False
                        
                # Register fallback chains for tool categories
                fallbacks = []
                if category == "search_tools":
                    fallbacks = ["duckduckgo_search", "utility_tools"]
                elif category == "blockchain_tools":
                    fallbacks = ["utility_tools"]
                    
                self.healing_agent.register_component(
                    category,
                    health_check=category_health_check,
                    fallbacks=fallbacks,
                    recovery_strategies=[
                        lambda comp, ft: self._recover_tool_category(category),
                        lambda comp, ft: self._fallback_tool_execution(category)
                    ]
                )
                
    async def _register_health_monitoring(self):
        """Register comprehensive health monitoring for all components."""
        
        # Register external API health checks
        await self._register_external_api_health()
        
        # Register system resource monitoring
        await self._register_system_health()
        
        print("✅ Health monitoring registered for all components")
        
    async def _register_external_api_health(self):
        """Register health monitoring for external APIs."""
        
        # Tavily API health check
        async def tavily_health_check():
            try:
                import os
                api_key = os.getenv("TAVILY_API_KEY")
                if not api_key:
                    return False
                    
                import aiohttp
                async with aiohttp.ClientSession() as session:
                    # Simple connectivity test
                    async with session.get("https://api.tavily.com/", timeout=5) as response:
                        return response.status < 500
            except Exception:
                return False
                
        # Moralis API health check
        async def moralis_health_check():
            try:
                import os
                api_key = os.getenv("MORALIS_API_KEY")
                if not api_key:
                    return False
                    
                import aiohttp
                async with aiohttp.ClientSession() as session:
                    headers = {"X-API-Key": api_key}
                    async with session.get("https://deep-index.moralis.io/api/v2/dateToBlock", 
                                         headers=headers, timeout=5) as response:
                        return response.status < 500
            except Exception:
                return False
                
        self.healing_agent.register_component("tavily_api", health_check=tavily_health_check)
        self.healing_agent.register_component("moralis_api", health_check=moralis_health_check)
        
    async def _register_system_health(self):
        """Register system resource health monitoring."""
        
        async def system_health_check():
            try:
                import psutil
                memory = psutil.virtual_memory()
                cpu = psutil.cpu_percent(interval=0.1)
                
                # System is healthy if memory < 90% and CPU < 95%
                return memory.percent < 90 and cpu < 95
            except Exception:
                return False
                
        self.healing_agent.register_component(
            "system_resources",
            health_check=system_health_check,
            recovery_strategies=[self._cleanup_system_resources]
        )
        
    # Recovery Methods
    async def _recover_model_connection(self, component: str, failure_type: FailureType) -> bool:
        """Recover model connection."""
        try:
            self.logger.info("Attempting to recover model connection")
            
            # Reinitialize the model
            self._setup_model()
            
            # Test the connection
            test_response = await asyncio.wait_for(
                self.model.ainvoke([HumanMessage(content="test")]),
                timeout=10.0
            )
            
            return True
        except Exception as e:
            self.logger.error(f"Model recovery failed: {e}")
            return False
            
    async def _restart_mcp_server(self, server_name: str) -> bool:
        """Attempt to restart an MCP server."""
        try:
            self.logger.info(f"Attempting to restart MCP server: {server_name}")
            
            # Get fresh server configurations
            server_configs = get_mcp_server_configs(include_external=True)
            
            if server_name in server_configs:
                # Reinitialize the specific server
                from mcp_config import clean_config_for_mcp
                clean_configs = clean_config_for_mcp({server_name: server_configs[server_name]})
                
                # This is a simplified restart - in practice, you might need more sophisticated logic
                await asyncio.sleep(2)  # Brief pause
                
                return True
            
            return False
        except Exception as e:
            self.logger.error(f"Server restart failed for {server_name}: {e}")
            return False
            
    async def _fallback_to_minimal_config(self) -> bool:
        """Fallback to minimal configuration."""
        try:
            self.logger.info("Falling back to minimal configuration")
            
            from mcp_config import MINIMAL_CONFIG, clean_config_for_mcp
            clean_minimal = clean_config_for_mcp(MINIMAL_CONFIG)
            
            self._setup_mcp_client(clean_minimal)
            self.tools = await self.client.get_tools()
            
            print(f"✅ Fallback successful: Loaded {len(self.tools)} tools from local server only")
            return True
            
        except Exception as e:
            self.logger.error(f"Fallback to minimal config failed: {e}")
            return False
            
    async def _recover_tool_category(self, category: str) -> bool:
        """Recover a specific tool category."""
        try:
            self.logger.info(f"Attempting to recover tool category: {category}")
            
            # Refresh tools from MCP servers
            self.tools = await self.client.get_tools()
            
            # Verify the category has tools
            if category == "utility_tools":
                return any("add" in t.name or "text" in t.name for t in self.tools)
            elif category == "search_tools":
                return any("search" in t.name for t in self.tools)
            elif category == "blockchain_tools":
                return any("evm_" in t.name or "solana_" in t.name for t in self.tools)
            elif category == "thinking_tools":
                return any("thinking" in t.name for t in self.tools)
                
            return True
            
        except Exception as e:
            self.logger.error(f"Tool category recovery failed for {category}: {e}")
            return False
            
    async def _fallback_tool_execution(self, category: str) -> bool:
        """Provide fallback tool execution for a category."""
        try:
            self.logger.info(f"Setting up fallback execution for {category}")
            
            # This would implement category-specific fallback logic
            # For now, we'll just ensure utility tools are available
            if category != "utility_tools":
                utility_tools = [t for t in self.tools if any(name in t.name for name in ["add", "multiply", "text"])]
                return len(utility_tools) > 0
                
            return True
            
        except Exception as e:
            self.logger.error(f"Fallback tool execution setup failed for {category}: {e}")
            return False
            
    async def _cleanup_system_resources(self, component: str, failure_type: FailureType) -> bool:
        """Clean up system resources."""
        try:
            self.logger.info("Attempting system resource cleanup")
            
            import gc
            gc.collect()  # Force garbage collection
            
            # Additional cleanup could be added here
            await asyncio.sleep(1)
            
            return True
            
        except Exception as e:
            self.logger.error(f"System cleanup failed: {e}")
            return False

    # Enhanced Tool Execution with Healing
    async def _enhanced_tool_node_with_healing(self, state: AgentState) -> AgentState:
        """Enhanced tool node with self-healing protection."""
        messages = state.get("messages", [])

        try:
            # Execute tools with healing protection
            result = await self.healing_agent.execute_with_healing(
                "tool_execution",
                self._execute_tools_safely,
                messages
            )

            # Update state with results
            updated_state = {
                **state,
                "messages": result.get("messages", messages)
            }

            # Process tool outputs with healing
            final_state = await self.healing_agent.execute_with_healing(
                "tool_processing",
                self._process_tool_outputs_safely,
                updated_state
            )

            return final_state

        except Exception as e:
            self.logger.error(f"Tool execution with healing failed: {e}")

            # Fallback to basic tool execution
            return await self._fallback_tool_execution_state(state)

    async def _execute_tools_safely(self, messages: List) -> Dict[str, Any]:
        """Execute tools with safety checks."""
        from langgraph.prebuilt import ToolNode

        tool_node = ToolNode(self.tools)
        messages_state = {"messages": messages}

        # Execute with timeout
        result = await asyncio.wait_for(
            tool_node.ainvoke(messages_state),
            timeout=30.0
        )

        return result

    async def _process_tool_outputs_safely(self, state: AgentState) -> AgentState:
        """Process tool outputs with safety checks."""
        return self._process_tool_outputs(state)

    async def _fallback_tool_execution_state(self, state: AgentState) -> AgentState:
        """Fallback tool execution when healing fails."""
        self.logger.warning("Using fallback tool execution")

        # Return state with error message
        error_message = ToolMessage(
            content="Tool execution failed, but system is recovering. Please try again.",
            tool_call_id="fallback"
        )

        return {
            **state,
            "messages": state.get("messages", []) + [error_message]
        }

    # Enhanced Invoke with Context7 Patterns and Healing
    async def invoke(self, message: str, config: Optional[RunnableConfig] = None) -> Dict[str, Any]:
        """
        Invoke the enhanced agent with Context7 patterns and self-healing protection.
        Supports both original and enhanced graph execution.
        """
        start_time = time.time()

        try:
            # Prepare enhanced input state
            input_state: InputState = {
                "query": message,
                "user_id": config.get("configurable", {}).get("user_id") if config else None,
                "session_id": config.get("configurable", {}).get("thread_id") if config else None,
                "execution_profile": config.get("configurable", {}).get("execution_profile", "balanced") if config else "balanced"
            }

            # Use enhanced graph if available, fallback to original
            if self.enhanced_graph:
                result = await self.healing_agent.execute_with_healing(
                    "enhanced_agent_invoke",
                    self._invoke_enhanced_safely,
                    input_state,
                    config
                )
            else:
                # Fallback to original invoke
                result = await self.healing_agent.execute_with_healing(
                    "agent_invoke",
                    self._invoke_safely,
                    message,
                    config
                )

            # Add comprehensive healing metadata
            healing_metadata = {
                "execution_time": time.time() - start_time,
                "health_status": self.get_health_status(),
                "recovery_events": self.get_recent_recovery_events(),
                "graph_type": "enhanced" if self.enhanced_graph else "original",
                "tool_performance": self.tool_performance_metrics if hasattr(self, 'tool_performance_metrics') else {}
            }

            # Ensure result has healing metadata
            if isinstance(result, dict):
                result["healing_metadata"] = healing_metadata
            else:
                result = {
                    "final_response": str(result),
                    "healing_metadata": healing_metadata
                }

            return result

        except Exception as e:
            self.logger.error(f"Agent invocation failed: {e}")

            # Return error response with healing information
            return {
                "input": message,
                "final_response": f"I encountered an error but my self-healing systems are working to resolve it. Error: {str(e)}",
                "error": True,
                "healing_metadata": {
                    "execution_time": time.time() - start_time,
                    "health_status": self.get_health_status(),
                    "recovery_events": self.get_recent_recovery_events(),
                    "error_details": str(e)
                }
            }

    async def _invoke_enhanced_safely(self, input_state: InputState, config: Optional[RunnableConfig]) -> Dict[str, Any]:
        """Safely invoke the enhanced graph"""
        try:
            result = await self.enhanced_graph.ainvoke(input_state, config)
            return result
        except Exception as e:
            self.logger.error(f"Enhanced graph execution failed: {e}")
            # Fallback to original graph
            return await self._invoke_safely(input_state.get("query", ""), config)

    async def stream(self, message: str, config: Optional[RunnableConfig] = None):
        """Stream agent responses in real-time with Context7 patterns"""
        try:
            # Prepare enhanced input state
            input_state: InputState = {
                "query": message,
                "user_id": config.get("configurable", {}).get("user_id") if config else None,
                "session_id": config.get("configurable", {}).get("thread_id") if config else None,
                "execution_profile": config.get("configurable", {}).get("execution_profile", "balanced") if config else "balanced"
            }

            # Stream from enhanced graph if available
            if self.enhanced_graph:
                async for chunk in self.enhanced_graph.astream(input_state, config, stream_mode="values"):
                    yield chunk
            else:
                # Fallback to single response
                result = await self.invoke(message, config)
                yield result

        except Exception as e:
            self.logger.error(f"Error streaming agent response: {e}")
            yield {
                "final_response": f"Error processing query: {str(e)}",
                "error": True,
                "healing_metadata": {
                    "health_status": self.get_health_status(),
                    "recovery_events": self.get_recent_recovery_events()
                }
            }

    async def _invoke_safely(self, message: str, config=None) -> Dict[str, Any]:
        """Safely invoke the parent agent."""
        return await super().invoke(message, config)

    # Health Status and Reporting
    def get_health_status(self) -> Dict[str, Any]:
        """Get current health status of all components."""
        return self.healing_agent.get_health_report()

    def get_recent_recovery_events(self) -> List[Dict[str, Any]]:
        """Get recent recovery events."""
        recent_events = []

        # Get events from the last hour
        cutoff_time = datetime.now() - timedelta(hours=1)

        for event in self.healing_agent.recovery_manager.recovery_history:
            if event["timestamp"] > cutoff_time:
                recent_events.append({
                    "timestamp": event["timestamp"].isoformat(),
                    "component": event["component"],
                    "failure_type": event["failure_type"],
                    "recovery_time": event["recovery_time"],
                    "success": event["success"]
                })

        return recent_events

    def get_circuit_breaker_status(self) -> Dict[str, str]:
        """Get status of all circuit breakers."""
        return {
            name: breaker.state
            for name, breaker in self.healing_agent.health_monitor.circuit_breakers.items()
        }

    async def force_health_check(self) -> Dict[str, Any]:
        """Force a health check of all components."""
        await self.healing_agent.health_monitor._check_all_components()
        return self.get_health_status()

    async def trigger_recovery(self, component: str) -> bool:
        """Manually trigger recovery for a specific component."""
        try:
            return await self.healing_agent.recovery_manager.attempt_recovery(
                component, FailureType.CONNECTION_FAILURE
            )
        except Exception as e:
            self.logger.error(f"Manual recovery trigger failed for {component}: {e}")
            return False

    # Override the graph building to use healing-enabled nodes
    def _build_graph(self):
        """Build the enhanced graph with self-healing nodes."""
        from langgraph.graph import StateGraph, END

        # Create the state graph
        workflow = StateGraph(AgentState)

        # Add nodes (using healing-enabled tool node)
        workflow.add_node("router", self._query_router_node)
        workflow.add_node("planner", self._task_planning_node)
        workflow.add_node("tools", self._enhanced_tool_node_with_healing)  # Healing-enabled
        workflow.add_node("direct_executor", self._direct_execution_node)
        workflow.add_node("synthesizer", self._synthesis_node)

        # Define the flow (same as parent)
        workflow.set_entry_point("router")

        workflow.add_conditional_edges(
            "router",
            self._route_from_router,
            {
                "direct": "direct_executor",
                "multi_step": "planner"
            }
        )

        workflow.add_edge("direct_executor", END)

        workflow.add_conditional_edges(
            "planner",
            self._route_from_planner,
            {
                "tools": "tools",
                "synthesizer": "synthesizer"
            }
        )

        workflow.add_edge("tools", "planner")
        workflow.add_edge("synthesizer", END)

        # Compile the graph
        self.graph = workflow.compile()

        print("✅ Self-healing enhanced reasoning graph compiled successfully")

    def _task_planning_node(self, state: AgentState) -> AgentState:
        """Task planning node for the original graph"""
        # Simple planning implementation
        messages = state.get("messages", [])
        if messages:
            last_message = messages[-1]
            plan = ["Analyze request", "Execute tools", "Synthesize response"]
            return {
                "plan": plan,
                "current_step": 0,
                "planning_cycles": 1
            }
        return {}

    def _route_from_router(self, state: AgentState) -> str:
        """Route from router node"""
        messages = state.get("messages", [])
        if messages:
            content = messages[-1].content.lower()
            if any(keyword in content for keyword in ['analyze', 'compare', 'detailed']):
                return "multi_step"
        return "direct"

    def _route_from_planner(self, state: AgentState) -> str:
        """Route from planner node"""
        current_step = state.get("current_step", 0)
        plan = state.get("plan", [])

        if current_step < len(plan) - 1:
            return "tools"
        else:
            return "synthesizer"

    async def _build_enhanced_graph(self):
        """Build enhanced graph with Context7 architecture patterns"""
        try:
            # Create enhanced state graph with sophisticated routing
            builder = StateGraph(EnhancedAgentState)

            # Add core processing nodes
            builder.add_node("query_analyzer", self._enhanced_query_analyzer)
            builder.add_node("execution_planner", self._enhanced_execution_planner)
            builder.add_node("tool_router", self._enhanced_tool_router)
            builder.add_node("result_synthesizer", self._enhanced_result_synthesizer)
            builder.add_node("memory_manager", self._enhanced_memory_manager)

            # Add specialist agent nodes if available
            for name, agent in self.specialist_agents.items():
                builder.add_node(f"{name}_specialist", agent)

            # Define sophisticated routing
            builder.add_edge(START, "query_analyzer")
            builder.add_conditional_edges(
                "query_analyzer",
                self._route_from_analyzer,
                {
                    "plan": "execution_planner",
                    "crypto": "crypto_specialist" if "crypto" in self.specialist_agents else "tool_router",
                    "search": "search_specialist" if "search" in self.specialist_agents else "tool_router",
                    "direct": "tool_router"
                }
            )

            builder.add_edge("execution_planner", "tool_router")
            builder.add_edge("tool_router", "result_synthesizer")
            builder.add_edge("result_synthesizer", "memory_manager")
            builder.add_edge("memory_manager", END)

            # Add specialist returns only if they exist
            for name in self.specialist_agents.keys():
                builder.add_edge(f"{name}_specialist", "result_synthesizer")

            # Compile with enhanced features
            self.enhanced_graph = builder.compile(
                checkpointer=self.checkpointer,
                store=self.store
            )

            self.logger.info("Enhanced graph with Context7 patterns compiled successfully")

        except Exception as e:
            self.logger.error(f"Failed to build enhanced graph: {e}")
            # Fallback to original graph
            self.enhanced_graph = None

    # Enhanced Node Functions with Context7 Patterns

    async def _enhanced_query_analyzer(self, state: EnhancedAgentState) -> EnhancedAgentState:
        """Analyze query with sophisticated routing logic"""
        query = state.get("query", "")
        execution_profile = state.get("execution_profile", "balanced")

        # Analyze query complexity and domain
        complexity_score = len(query.split()) / 10.0  # Simple complexity metric

        # Determine agent mode based on content analysis
        query_lower = query.lower()
        if any(keyword in query_lower for keyword in ['wallet', 'token', 'nft', 'defi', 'crypto', 'blockchain']):
            agent_mode = "crypto"
        elif any(keyword in query_lower for keyword in ['search', 'news', 'find', 'research', 'latest']):
            agent_mode = "search"
        elif complexity_score > 2.0 or any(keyword in query_lower for keyword in ['analyze', 'compare', 'evaluate']):
            agent_mode = "plan"
        else:
            agent_mode = "direct"

        # Initialize planning state for complex queries
        planning_state: PlanningState = {
            "execution_plan": [],
            "current_step": 0,
            "reasoning_history": [f"Query analyzed: complexity={complexity_score:.2f}, mode={agent_mode}"],
            "tool_selection_rationale": [],
            "plan_confidence": 0.8,
            "adaptive_adjustments": []
        }

        return {
            "agent_mode": agent_mode,
            "planning": planning_state,
            "messages": [HumanMessage(content=query)]
        }

    def _route_from_analyzer(self, state: EnhancedAgentState) -> str:
        """Route based on analysis results"""
        mode = state.get("agent_mode", "direct")

        # Check if specialist agents are available
        if mode == "crypto" and "crypto" in self.specialist_agents:
            return "crypto"
        elif mode == "search" and "search" in self.specialist_agents:
            return "search"
        elif mode == "plan":
            return "plan"
        else:
            return "direct"

    async def _enhanced_execution_planner(self, state: EnhancedAgentState) -> EnhancedAgentState:
        """Create sophisticated execution plan"""
        query = state.get("query", "")
        execution_profile = state.get("execution_profile", "balanced")
        profile_config = self.execution_profiles.get(execution_profile, self.execution_profiles["balanced"])

        # Create execution plan based on query analysis
        plan_steps = []
        query_lower = query.lower()

        if "wallet" in query_lower:
            plan_steps.extend(["Analyze wallet address", "Get balance and transactions", "Check NFT holdings"])
        if "token" in query_lower:
            plan_steps.extend(["Get token metadata", "Analyze price and volume", "Check holder distribution"])
        if "compare" in query_lower or "vs" in query_lower:
            plan_steps.append("Perform comparative analysis")
        if any(keyword in query_lower for keyword in ['news', 'latest', 'recent']):
            plan_steps.append("Search for recent information")

        # Limit steps based on execution profile
        plan_steps = plan_steps[:profile_config["max_steps"]]

        planning_state: PlanningState = {
            "execution_plan": plan_steps,
            "current_step": 0,
            "reasoning_history": [f"Created {len(plan_steps)}-step execution plan for {execution_profile} profile"],
            "tool_selection_rationale": [],
            "plan_confidence": 0.9,
            "adaptive_adjustments": []
        }

        return {"planning": planning_state}

    async def _enhanced_tool_router(self, state: EnhancedAgentState) -> EnhancedAgentState:
        """Route and execute tools based on planning"""
        query = state.get("query", "")
        planning = state.get("planning", {})
        execution_profile = state.get("execution_profile", "balanced")

        # Use the actual model to generate response
        try:
            if hasattr(self, 'model') and self.model:
                # Create a proper prompt for Claude
                prompt = f"You are Claude Sonnet 4, an advanced AI assistant. Please respond to this query: {query}"
                response = await self.model.ainvoke([HumanMessage(content=prompt)])

                execution_state: ExecutionState = {
                    "tools_executed": ["claude_model"],
                    "tool_results": {"claude_model": response.content},
                    "execution_errors": [],
                    "performance_metrics": {"execution_time": 1.0},
                    "circuit_breaker_states": {},
                    "recovery_events": []
                }

                return {
                    "execution": execution_state,
                    "messages": [response]
                }
            else:
                # Fallback to simulated response
                execution_state: ExecutionState = {
                    "tools_executed": ["simulated_tool"],
                    "tool_results": {"simulated_tool": f"Model not available. Query: {query[:100]}..."},
                    "execution_errors": ["Model not initialized"],
                    "performance_metrics": {"execution_time": 0.1},
                    "circuit_breaker_states": {},
                    "recovery_events": []
                }

                return {
                    "execution": execution_state,
                    "messages": [AIMessage(content=f"Model not available. Query was: {query}")]
                }
        except Exception as e:
            # Error handling
            execution_state: ExecutionState = {
                "tools_executed": ["error_handler"],
                "tool_results": {"error_handler": f"Error: {str(e)}"},
                "execution_errors": [str(e)],
                "performance_metrics": {"execution_time": 0.1},
                "circuit_breaker_states": {},
                "recovery_events": []
            }

            return {
                "execution": execution_state,
                "messages": [AIMessage(content=f"I encountered an error: {str(e)}")]
            }

    async def _enhanced_result_synthesizer(self, state: EnhancedAgentState) -> EnhancedAgentState:
        """Synthesize results into final response"""
        messages = state.get("messages", [])
        execution = state.get("execution", {})
        planning = state.get("planning", {})

        # Create comprehensive response
        response_parts = []

        if messages:
            last_ai_message = next((m for m in reversed(messages) if isinstance(m, AIMessage)), None)
            if last_ai_message:
                response_parts.append(last_ai_message.content)

        # Add execution summary
        tools_used = execution.get("tools_executed", [])
        if tools_used:
            response_parts.append(f"\n\nTools used: {', '.join(tools_used)}")

        # Add reasoning if available
        reasoning = planning.get("reasoning_history", [])

        final_response = "\n".join(response_parts) if response_parts else "No response generated"

        return {
            "final_response": final_response,
            "reasoning_steps": reasoning,
            "tools_used": tools_used,
            "execution_time": execution.get("performance_metrics", {}).get("execution_time", 0.0)
        }

    async def _enhanced_memory_manager(self, state: EnhancedAgentState) -> EnhancedAgentState:
        """Manage conversation memory and context"""
        messages = state.get("messages", [])
        if messages:
            # Simple memory management - can be enhanced
            summary = f"Processed query at {datetime.now().isoformat()}"

            memory_state: MemoryState = {
                "conversation_summary": summary,
                "key_insights": [],
                "user_preferences": {},
                "domain_expertise": {},
                "learning_feedback": []
            }

            return {"memory": memory_state}

        return {}

    async def cleanup(self):
        """Cleanup resources including healing system."""
        await self.healing_agent.shutdown()
        await super().cleanup()

# Enhanced Utility function for easy self-healing agent creation with Context7 features
async def create_self_healing_agent(
    server_configs: Dict[str, Dict[str, Any]] = None,
    use_persistent_memory: bool = False,
    memory_backend: str = "memory"
) -> SelfHealingEnhancedAgent:
    """
    Create and initialize a Self-Healing Enhanced LangGraph MCP agent with Context7 optimizations.

    Args:
        server_configs: MCP server configurations
        use_persistent_memory: Enable persistent memory with checkpointers
        memory_backend: Memory backend type ("memory", "redis", "postgres")

    Returns:
        Initialized SelfHealingEnhancedAgent with enhanced capabilities
    """
    agent = SelfHealingEnhancedAgent()
    await agent.initialize(server_configs, use_persistent_memory, memory_backend)
    return agent

    # Enhanced Tool Execution with Healing
    async def _enhanced_tool_node_with_healing(self, state: AgentState) -> AgentState:
        """Enhanced tool node with self-healing protection."""
        messages = state.get("messages", [])

        try:
            # Execute tools with healing protection
            result = await self.healing_agent.execute_with_healing(
                "tool_execution",
                self._execute_tools_safely,
                messages
            )

            # Update state with results
            updated_state = {
                **state,
                "messages": result.get("messages", messages)
            }

            # Process tool outputs with healing
            final_state = await self.healing_agent.execute_with_healing(
                "tool_processing",
                self._process_tool_outputs_safely,
                updated_state
            )

            return final_state

        except Exception as e:
            self.logger.error(f"Tool execution with healing failed: {e}")

            # Fallback to basic tool execution
            return await self._fallback_tool_execution_state(state)

    async def _execute_tools_safely(self, messages: List) -> Dict[str, Any]:
        """Execute tools with safety checks."""
        from langgraph.prebuilt import ToolNode

        tool_node = ToolNode(self.tools)
        messages_state = {"messages": messages}

        # Execute with timeout
        result = await asyncio.wait_for(
            tool_node.ainvoke(messages_state),
            timeout=30.0
        )

        return result

    async def _process_tool_outputs_safely(self, state: AgentState) -> AgentState:
        """Process tool outputs with safety checks."""
        return self._process_tool_outputs(state)

    async def _fallback_tool_execution_state(self, state: AgentState) -> AgentState:
        """Fallback tool execution when healing fails."""
        self.logger.warning("Using fallback tool execution")

        # Return state with error message
        error_message = ToolMessage(
            content="Tool execution failed, but system is recovering. Please try again.",
            tool_call_id="fallback"
        )

        return {
            **state,
            "messages": state.get("messages", []) + [error_message]
        }

    # Enhanced Invoke with Healing
    async def invoke(self, message: str, config=None) -> Dict[str, Any]:
        """Invoke the enhanced agent with self-healing protection."""
        start_time = time.time()

        try:
            # Execute with healing protection
            result = await self.healing_agent.execute_with_healing(
                "agent_invoke",
                self._invoke_safely,
                message,
                config
            )

            # Add healing metadata to result
            result["healing_metadata"] = {
                "execution_time": time.time() - start_time,
                "health_status": self.get_health_status(),
                "recovery_events": self.get_recent_recovery_events()
            }

            return result

        except Exception as e:
            self.logger.error(f"Agent invocation failed: {e}")

            # Return error response with healing information
            return {
                "input": message,
                "final_response": f"I encountered an error but my self-healing systems are working to resolve it. Error: {str(e)}",
                "error": True,
                "healing_metadata": {
                    "execution_time": time.time() - start_time,
                    "health_status": self.get_health_status(),
                    "recovery_events": self.get_recent_recovery_events()
                }
            }

    async def _invoke_safely(self, message: str, config=None) -> Dict[str, Any]:
        """Safely invoke the parent agent."""
        return await super().invoke(message, config)

    # Health Status and Reporting
    def get_health_status(self) -> Dict[str, Any]:
        """Get current health status of all components."""
        return self.healing_agent.get_health_report()

    def get_recent_recovery_events(self) -> List[Dict[str, Any]]:
        """Get recent recovery events."""
        recent_events = []

        # Get events from the last hour
        cutoff_time = datetime.now() - timedelta(hours=1)

        for event in self.healing_agent.recovery_manager.recovery_history:
            if event["timestamp"] > cutoff_time:
                recent_events.append({
                    "timestamp": event["timestamp"].isoformat(),
                    "component": event["component"],
                    "failure_type": event["failure_type"],
                    "recovery_time": event["recovery_time"],
                    "success": event["success"]
                })

        return recent_events

    def get_circuit_breaker_status(self) -> Dict[str, str]:
        """Get status of all circuit breakers."""
        return {
            name: breaker.state
            for name, breaker in self.healing_agent.health_monitor.circuit_breakers.items()
        }

    async def force_health_check(self) -> Dict[str, Any]:
        """Force a health check of all components."""
        await self.healing_agent.health_monitor._check_all_components()
        return self.get_health_status()

    async def trigger_recovery(self, component: str) -> bool:
        """Manually trigger recovery for a specific component."""
        try:
            return await self.healing_agent.recovery_manager.attempt_recovery(
                component, FailureType.CONNECTION_FAILURE
            )
        except Exception as e:
            self.logger.error(f"Manual recovery trigger failed for {component}: {e}")
            return False

    # Override the graph building to use healing-enabled nodes
    def _build_graph(self):
        """Build the enhanced graph with self-healing nodes."""
        from langgraph.graph import StateGraph, END

        # Create the state graph
        workflow = StateGraph(AgentState)

        # Add nodes (using healing-enabled tool node)
        workflow.add_node("router", self._query_router_node)
        workflow.add_node("planner", self._task_planning_node)
        workflow.add_node("tools", self._enhanced_tool_node_with_healing)  # Healing-enabled
        workflow.add_node("direct_executor", self._direct_execution_node)
        workflow.add_node("synthesizer", self._synthesis_node)

        # Define the flow (same as parent)
        workflow.set_entry_point("router")

        workflow.add_conditional_edges(
            "router",
            self._route_from_router,
            {
                "direct": "direct_executor",
                "multi_step": "planner"
            }
        )

        workflow.add_edge("direct_executor", END)

        workflow.add_conditional_edges(
            "planner",
            self._route_from_planner,
            {
                "tools": "tools",
                "synthesizer": "synthesizer"
            }
        )

        workflow.add_edge("tools", "planner")
        workflow.add_edge("synthesizer", END)

        # Compile the graph
        self.graph = workflow.compile()

        print("✅ Self-healing enhanced reasoning graph compiled successfully")

    async def cleanup(self):
        """Cleanup resources including healing system."""
        await self.healing_agent.shutdown()
        await super().cleanup()


