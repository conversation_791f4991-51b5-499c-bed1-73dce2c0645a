#!/usr/bin/env python3
"""
Multi-Modal Fusion System for Phase 3 Enhanced Capabilities
Enables processing of text, images, audio, and video with cross-modal correlation.
"""

import asyncio
import base64
import io
import json
import os
from typing import Dict, Any, List, Optional, Union, Tuple
from dataclasses import dataclass, field
from datetime import datetime
from PIL import Image
import requests

from langchain_openai import ChatOpenAI
from langchain_core.messages import HumanMessage

@dataclass
class ImageAnalysisResult:
    """Result of image analysis."""
    description: str
    objects_detected: List[str] = field(default_factory=list)
    text_extracted: str = ""
    chart_data: Dict[str, Any] = field(default_factory=dict)
    sentiment: str = "neutral"
    confidence: float = 0.0
    analysis_type: str = "general"
    timestamp: datetime = field(default_factory=datetime.now)

@dataclass
class AudioAnalysisResult:
    """Result of audio analysis."""
    transcript: str
    speaker_count: int = 1
    sentiment: str = "neutral"
    key_topics: List[str] = field(default_factory=list)
    confidence: float = 0.0
    duration: float = 0.0
    language: str = "en"
    timestamp: datetime = field(default_factory=datetime.now)

@dataclass
class VideoAnalysisResult:
    """Result of video analysis."""
    summary: str
    key_frames: List[Dict[str, Any]] = field(default_factory=list)
    audio_analysis: Optional[AudioAnalysisResult] = None
    visual_analysis: List[ImageAnalysisResult] = field(default_factory=list)
    duration: float = 0.0
    topics: List[str] = field(default_factory=list)
    timestamp: datetime = field(default_factory=datetime.now)

@dataclass
class CrossModalInsight:
    """Cross-modal correlation insight."""
    insight_type: str
    description: str
    confidence: float
    supporting_modalities: List[str]
    evidence: Dict[str, Any] = field(default_factory=dict)
    risk_level: str = "low"
    actionable: bool = False

class MultiModalProcessor:
    """Advanced multi-modal processing system for crypto intelligence."""
    
    def __init__(self, openai_api_key: Optional[str] = None):
        self.openai_api_key = openai_api_key or os.getenv("OPENAI_API_KEY")
        self.vision_model = None
        self.audio_model = None
        
        if self.openai_api_key:
            self.vision_model = ChatOpenAI(
                model="gpt-4o",  # GPT-4 with vision
                api_key=self.openai_api_key,
                temperature=0.1
            )
        
        # Analysis capabilities
        self.supported_image_formats = ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp']
        self.supported_audio_formats = ['.mp3', '.wav', '.m4a', '.ogg']
        self.supported_video_formats = ['.mp4', '.avi', '.mov', '.mkv']
        
    async def analyze_image(self, image_input: Union[str, bytes, Image.Image], 
                          analysis_type: str = "crypto_chart") -> ImageAnalysisResult:
        """Analyze image with crypto-specific intelligence."""
        
        if not self.vision_model:
            return ImageAnalysisResult(
                description="Vision analysis not available - OpenAI API key required",
                confidence=0.0
            )
        
        try:
            # Convert image to base64 for API
            image_b64 = self._prepare_image_for_analysis(image_input)
            
            # Create analysis prompt based on type
            prompt = self._create_image_analysis_prompt(analysis_type)
            
            # Analyze with vision model
            message = HumanMessage(
                content=[
                    {"type": "text", "text": prompt},
                    {"type": "image_url", "image_url": {"url": f"data:image/jpeg;base64,{image_b64}"}}
                ]
            )
            
            response = await self.vision_model.ainvoke([message])
            analysis_text = response.content
            
            # Parse structured response
            result = self._parse_image_analysis_response(analysis_text, analysis_type)
            
            print(f"🖼️  Image Analysis Complete: {analysis_type}")
            return result
            
        except Exception as e:
            print(f"❌ Image analysis failed: {e}")
            return ImageAnalysisResult(
                description=f"Image analysis failed: {e}",
                confidence=0.0
            )
    
    def _prepare_image_for_analysis(self, image_input: Union[str, bytes, Image.Image]) -> str:
        """Prepare image for vision API analysis."""
        
        if isinstance(image_input, str):
            # URL or file path
            if image_input.startswith(('http://', 'https://')):
                # Download image from URL
                response = requests.get(image_input)
                image_bytes = response.content
            else:
                # Read from file
                with open(image_input, 'rb') as f:
                    image_bytes = f.read()
        elif isinstance(image_input, bytes):
            image_bytes = image_input
        elif isinstance(image_input, Image.Image):
            # Convert PIL Image to bytes
            buffer = io.BytesIO()
            image_input.save(buffer, format='JPEG')
            image_bytes = buffer.getvalue()
        else:
            raise ValueError("Unsupported image input type")
        
        # Convert to base64
        return base64.b64encode(image_bytes).decode('utf-8')
    
    def _create_image_analysis_prompt(self, analysis_type: str) -> str:
        """Create specialized prompt for different image analysis types."""
        
        base_prompt = """You are an expert crypto analyst with advanced visual analysis capabilities. 
        Analyze this image and provide detailed insights in JSON format."""
        
        if analysis_type == "crypto_chart":
            return base_prompt + """
            
            CHART ANALYSIS FOCUS:
            1. **Price Movement**: Identify trends, support/resistance levels, patterns
            2. **Technical Indicators**: RSI, MACD, moving averages, volume
            3. **Market Sentiment**: Bullish/bearish signals, breakout patterns
            4. **Time Frame**: Identify the chart timeframe (1h, 4h, 1d, etc.)
            5. **Key Levels**: Important price levels and zones
            
            Return JSON with:
            {
                "description": "Detailed chart analysis",
                "chart_data": {
                    "trend": "bullish/bearish/sideways",
                    "key_levels": ["support", "resistance"],
                    "indicators": {"rsi": "value", "macd": "signal"},
                    "timeframe": "detected timeframe",
                    "pattern": "chart pattern if any"
                },
                "sentiment": "bullish/bearish/neutral",
                "confidence": 0.0-1.0
            }
            """
        
        elif analysis_type == "nft_visual":
            return base_prompt + """
            
            NFT VISUAL ANALYSIS FOCUS:
            1. **Artistic Quality**: Style, composition, uniqueness
            2. **Rarity Indicators**: Rare traits, special attributes
            3. **Collection Consistency**: Fits collection style
            4. **Market Appeal**: Visual attractiveness, trend alignment
            5. **Authenticity**: Signs of originality vs. derivative work
            
            Return JSON with:
            {
                "description": "NFT visual analysis",
                "objects_detected": ["traits", "attributes"],
                "sentiment": "positive/negative/neutral",
                "confidence": 0.0-1.0
            }
            """
        
        elif analysis_type == "defi_interface":
            return base_prompt + """
            
            DEFI INTERFACE ANALYSIS FOCUS:
            1. **Protocol Identification**: Which DeFi protocol/dapp
            2. **Functionality**: What operations are available
            3. **Risk Indicators**: Warning signs, security features
            4. **User Experience**: Interface quality, usability
            5. **Data Extraction**: APY rates, TVL, fees visible
            
            Return JSON with:
            {
                "description": "DeFi interface analysis",
                "objects_detected": ["protocol_name", "features"],
                "text_extracted": "visible text and numbers",
                "confidence": 0.0-1.0
            }
            """
        
        else:
            return base_prompt + """
            
            GENERAL CRYPTO IMAGE ANALYSIS:
            1. **Content Identification**: What is shown in the image
            2. **Crypto Relevance**: How it relates to cryptocurrency/blockchain
            3. **Text Extraction**: Any visible text, numbers, or data
            4. **Context Clues**: Environmental or contextual information
            5. **Sentiment**: Overall positive/negative/neutral impression
            
            Return JSON with:
            {
                "description": "General image analysis",
                "objects_detected": ["main objects"],
                "text_extracted": "any visible text",
                "sentiment": "positive/negative/neutral",
                "confidence": 0.0-1.0
            }
            """
    
    def _parse_image_analysis_response(self, response_text: str, analysis_type: str) -> ImageAnalysisResult:
        """Parse the vision model response into structured result."""
        
        try:
            # Try to extract JSON from response
            if '{' in response_text and '}' in response_text:
                start = response_text.find('{')
                end = response_text.rfind('}') + 1
                json_str = response_text[start:end]
                data = json.loads(json_str)
            else:
                # Fallback to text parsing
                data = {"description": response_text, "confidence": 0.7}
            
            return ImageAnalysisResult(
                description=data.get("description", response_text),
                objects_detected=data.get("objects_detected", []),
                text_extracted=data.get("text_extracted", ""),
                chart_data=data.get("chart_data", {}),
                sentiment=data.get("sentiment", "neutral"),
                confidence=data.get("confidence", 0.7),
                analysis_type=analysis_type
            )
            
        except Exception as e:
            print(f"⚠️  Failed to parse image analysis response: {e}")
            return ImageAnalysisResult(
                description=response_text,
                confidence=0.5,
                analysis_type=analysis_type
            )
    
    async def analyze_audio(self, audio_input: Union[str, bytes], 
                          analysis_type: str = "crypto_content") -> AudioAnalysisResult:
        """Analyze audio content with crypto-specific intelligence."""
        
        # Placeholder for audio analysis - would integrate with Whisper API
        print(f"🎵 Audio Analysis: {analysis_type}")
        
        # Simulated audio analysis result
        return AudioAnalysisResult(
            transcript="[Audio analysis not yet implemented - placeholder]",
            sentiment="neutral",
            key_topics=["crypto", "analysis"],
            confidence=0.0
        )
    
    async def analyze_video(self, video_input: Union[str, bytes],
                          analysis_type: str = "crypto_content") -> VideoAnalysisResult:
        """Analyze video content with multi-modal intelligence."""
        
        # Placeholder for video analysis - would extract frames and audio
        print(f"🎬 Video Analysis: {analysis_type}")
        
        # Simulated video analysis result
        return VideoAnalysisResult(
            summary="[Video analysis not yet implemented - placeholder]",
            duration=0.0,
            topics=["crypto", "analysis"]
        )
    
    async def cross_modal_correlation(self, 
                                    text_data: str,
                                    image_results: List[ImageAnalysisResult] = None,
                                    audio_results: List[AudioAnalysisResult] = None,
                                    video_results: List[VideoAnalysisResult] = None) -> List[CrossModalInsight]:
        """Perform cross-modal correlation analysis."""
        
        insights = []
        
        # Analyze correlations between modalities
        if image_results and text_data:
            for img_result in image_results:
                if img_result.analysis_type == "crypto_chart":
                    # Correlate chart sentiment with text sentiment
                    insight = self._correlate_chart_with_text(img_result, text_data)
                    if insight:
                        insights.append(insight)
        
        # Add more correlation patterns as needed
        
        return insights
    
    def _correlate_chart_with_text(self, chart_result: ImageAnalysisResult, text_data: str) -> Optional[CrossModalInsight]:
        """Correlate chart analysis with text sentiment."""
        
        chart_sentiment = chart_result.sentiment
        
        # Simple text sentiment analysis (could be enhanced with proper NLP)
        text_lower = text_data.lower()
        if any(word in text_lower for word in ['bullish', 'pump', 'moon', 'buy']):
            text_sentiment = "bullish"
        elif any(word in text_lower for word in ['bearish', 'dump', 'crash', 'sell']):
            text_sentiment = "bearish"
        else:
            text_sentiment = "neutral"
        
        # Check for correlation or discrepancy
        if chart_sentiment == text_sentiment and chart_sentiment != "neutral":
            return CrossModalInsight(
                insight_type="correlation",
                description=f"Chart analysis and text sentiment both indicate {chart_sentiment} market conditions",
                confidence=0.8,
                supporting_modalities=["image", "text"],
                evidence={"chart_sentiment": chart_sentiment, "text_sentiment": text_sentiment},
                actionable=True
            )
        elif chart_sentiment != text_sentiment and "neutral" not in [chart_sentiment, text_sentiment]:
            return CrossModalInsight(
                insight_type="discrepancy",
                description=f"Discrepancy detected: Chart shows {chart_sentiment} but text indicates {text_sentiment}",
                confidence=0.7,
                supporting_modalities=["image", "text"],
                evidence={"chart_sentiment": chart_sentiment, "text_sentiment": text_sentiment},
                risk_level="medium",
                actionable=True
            )
        
        return None
    
    def get_supported_formats(self) -> Dict[str, List[str]]:
        """Get supported file formats for each media type."""
        return {
            "image": self.supported_image_formats,
            "audio": self.supported_audio_formats,
            "video": self.supported_video_formats
        }

    async def process_multi_modal_query(self, query: str, media_inputs: Dict[str, Any]) -> Dict[str, Any]:
        """Process a query with multiple media inputs."""

        results = {
            "query": query,
            "image_results": [],
            "audio_results": [],
            "video_results": [],
            "cross_modal_insights": [],
            "summary": ""
        }

        # Process images
        if "images" in media_inputs:
            for image_input in media_inputs["images"]:
                result = await self.analyze_image(image_input, "crypto_chart")
                results["image_results"].append(result)

        # Process audio
        if "audio" in media_inputs:
            for audio_input in media_inputs["audio"]:
                result = await self.analyze_audio(audio_input, "crypto_content")
                results["audio_results"].append(result)

        # Process video
        if "video" in media_inputs:
            for video_input in media_inputs["video"]:
                result = await self.analyze_video(video_input, "crypto_content")
                results["video_results"].append(result)

        # Perform cross-modal correlation
        insights = await self.cross_modal_correlation(
            text_data=query,
            image_results=results["image_results"],
            audio_results=results["audio_results"],
            video_results=results["video_results"]
        )
        results["cross_modal_insights"] = insights

        # Generate summary
        results["summary"] = self._generate_multi_modal_summary(results)

        return results

    def _generate_multi_modal_summary(self, results: Dict[str, Any]) -> str:
        """Generate summary of multi-modal analysis."""

        summary_parts = []

        if results["image_results"]:
            summary_parts.append(f"Analyzed {len(results['image_results'])} images")

        if results["audio_results"]:
            summary_parts.append(f"Processed {len(results['audio_results'])} audio files")

        if results["video_results"]:
            summary_parts.append(f"Analyzed {len(results['video_results'])} videos")

        if results["cross_modal_insights"]:
            summary_parts.append(f"Found {len(results['cross_modal_insights'])} cross-modal insights")

        return "Multi-modal analysis: " + ", ".join(summary_parts) if summary_parts else "No multi-modal content processed"
