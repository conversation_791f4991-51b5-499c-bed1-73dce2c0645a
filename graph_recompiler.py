#!/usr/bin/env python3
"""
Dynamic Graph Recompilation Engine
Enables runtime graph modification and optimization based on performance analysis.
"""

import asyncio
import inspect
from typing import Dict, Any, List, Optional, Callable
from datetime import datetime

from langgraph.graph import StateGraph, END, START
from langgraph.checkpoint.memory import MemorySaver

from enhanced_agent import Agent<PERSON>tate
from graph_templates import GraphTemplateManager, GraphTemplate, GraphType
from meta_reflector import MetaReflector, OptimizationDecision

class GraphRecompiler:
    """Engine for dynamic graph recompilation and optimization."""
    
    def __init__(self, agent_instance):
        self.agent = agent_instance
        self.template_manager = GraphTemplateManager()
        self.meta_reflector = MetaReflector(
            self.agent.performance_tracker, 
            self.template_manager
        )
        
        # Graph compilation cache
        self.compiled_graphs = {}
        self.active_graph_id = "base"
        
        # Safety mechanisms
        self.rollback_graphs = {}
        self.optimization_test_mode = False
        
    async def check_and_optimize_graph(self, state: AgentState) -> AgentState:
        """Check if graph optimization is needed and apply if beneficial."""
        
        # Perform meta-reflection
        optimization_decision = await self.meta_reflector.meta_reflect(state)
        
        if optimization_decision.optimization_type == "none":
            return state
        
        print(f"🧠 Meta-Reflector Decision: {optimization_decision.optimization_type}")
        print(f"   Confidence: {optimization_decision.confidence:.2f}")
        print(f"   Reasoning: {optimization_decision.reasoning}")
        
        # Apply optimization
        success = await self._apply_optimization(optimization_decision, state)
        
        if success:
            # Update state to reflect optimization
            state["graph_optimizations"] = state.get("graph_optimizations", []) + [
                {
                    "type": optimization_decision.optimization_type,
                    "timestamp": datetime.now().isoformat(),
                    "confidence": optimization_decision.confidence
                }
            ]
            state["meta_reflection_triggered"] = True
            
            print(f"✅ Graph optimization applied: {optimization_decision.optimization_type}")
        else:
            print(f"❌ Graph optimization failed: {optimization_decision.optimization_type}")
        
        return state
    
    async def _apply_optimization(self, decision: OptimizationDecision, state: AgentState) -> bool:
        """Apply the optimization decision."""
        
        try:
            if decision.optimization_type == "create_fast_track":
                return await self._create_fast_track_optimization()
            
            elif decision.optimization_type == "create_specialist_direct":
                return await self._create_specialist_direct_optimization()
            
            elif decision.optimization_type == "create_tool_chain":
                return await self._create_tool_chain_optimization(decision.new_template_spec)
            
            elif decision.optimization_type == "remove_inefficient_paths":
                return await self._remove_inefficient_paths_optimization(decision.new_template_spec)
            
            elif decision.optimization_type == "create_intent_specific_graph":
                return await self._create_intent_specific_optimization(decision.new_template_spec)
            
            else:
                print(f"⚠️  Unknown optimization type: {decision.optimization_type}")
                return False
                
        except Exception as e:
            print(f"❌ Optimization failed: {e}")
            return False
    
    async def _create_fast_track_optimization(self) -> bool:
        """Create and deploy fast-track template."""
        
        # Check if fast-track template already exists
        if self.template_manager.get_template("fast_track"):
            print("Fast-track template already exists, updating...")
            return True
        
        # Create fast-track template
        fast_track_template = self.template_manager.create_fast_track_template()
        
        # Register template
        self.template_manager.register_template(fast_track_template)
        
        # Compile the new graph
        compiled_graph = await self._compile_template(fast_track_template)
        if compiled_graph:
            self.compiled_graphs["fast_track"] = compiled_graph
            print("✅ Fast-track template created and compiled")
            return True
        
        return False
    
    async def _create_specialist_direct_optimization(self) -> bool:
        """Create and deploy specialist direct routing template."""
        
        specialist_template = self.template_manager.create_specialist_direct_template()
        self.template_manager.register_template(specialist_template)
        
        compiled_graph = await self._compile_template(specialist_template)
        if compiled_graph:
            self.compiled_graphs["specialist_direct"] = compiled_graph
            print("✅ Specialist direct template created and compiled")
            return True
        
        return False
    
    async def _create_tool_chain_optimization(self, spec: Dict[str, Any]) -> bool:
        """Create optimized tool chain template."""
        
        tool_sequence = spec.get("tool_sequence", [])
        if not tool_sequence:
            return False
        
        tool_chain_template = self.template_manager.create_tool_chain_template(tool_sequence)
        self.template_manager.register_template(tool_chain_template)
        
        # Create specialized tool chain executor node
        await self._create_tool_chain_executor_node(tool_sequence)
        
        compiled_graph = await self._compile_template(tool_chain_template)
        if compiled_graph:
            self.compiled_graphs[tool_chain_template.template_id] = compiled_graph
            print(f"✅ Tool chain template created: {' -> '.join(tool_sequence)}")
            return True
        
        return False
    
    async def _remove_inefficient_paths_optimization(self, spec: Dict[str, Any]) -> bool:
        """Create template with inefficient paths removed."""
        
        paths_to_remove = spec.get("paths_to_remove", [])
        if not paths_to_remove:
            return False
        
        # Create modified base template
        base_template = self.template_manager.get_template("base")
        if not base_template:
            return False
        
        # Create optimized template (simplified for demo)
        optimized_template = GraphTemplate(
            template_id="optimized_base",
            graph_type=GraphType.BASE,
            name="Optimized Base (Inefficient Paths Removed)",
            description=f"Base template with inefficient paths removed: {', '.join(paths_to_remove)}",
            node_definitions=base_template.node_definitions.copy(),
            edge_definitions=base_template.edge_definitions.copy(),
            conditional_edges=base_template.conditional_edges.copy(),
            entry_point=base_template.entry_point
        )
        
        self.template_manager.register_template(optimized_template)
        
        compiled_graph = await self._compile_template(optimized_template)
        if compiled_graph:
            self.compiled_graphs["optimized_base"] = compiled_graph
            print(f"✅ Optimized base template created (removed {len(paths_to_remove)} inefficient paths)")
            return True
        
        return False
    
    async def _create_intent_specific_optimization(self, spec: Dict[str, Any]) -> bool:
        """Create intent-specific optimized graph."""
        
        intent = spec.get("intent", "")
        optimized_path = spec.get("optimized_path", "")
        
        if not intent or not optimized_path:
            return False
        
        # Create intent-specific template
        intent_template = GraphTemplate(
            template_id=f"intent_{intent}",
            graph_type=GraphType.EXPERIMENTAL,
            name=f"Intent-Specific: {intent}",
            description=f"Optimized graph for {intent} queries using path: {optimized_path}",
            node_definitions={
                "router": "_query_router_node",
                "optimized_processor": "_intent_optimized_processor_node",
                "synthesizer": "_synthesis_node"
            },
            edge_definitions=[
                {"from": "router", "to": "optimized_processor"},
                {"from": "optimized_processor", "to": "synthesizer"},
                {"from": "synthesizer", "to": END}
            ],
            conditional_edges=[],
            entry_point="router"
        )
        
        self.template_manager.register_template(intent_template)
        
        compiled_graph = await self._compile_template(intent_template)
        if compiled_graph:
            self.compiled_graphs[intent_template.template_id] = compiled_graph
            print(f"✅ Intent-specific template created for: {intent}")
            return True
        
        return False
    
    async def _compile_template(self, template: GraphTemplate) -> Optional[StateGraph]:
        """Compile a graph template into an executable graph."""
        
        try:
            # Create new workflow
            workflow = StateGraph(AgentState)
            
            # Add nodes
            for node_name, method_name in template.node_definitions.items():
                if hasattr(self.agent, method_name):
                    node_method = getattr(self.agent, method_name)
                    workflow.add_node(node_name, node_method)
                else:
                    # Create placeholder or use default method
                    workflow.add_node(node_name, self._create_placeholder_node(node_name))
            
            # Add edges
            for edge in template.edge_definitions:
                workflow.add_edge(edge["from"], edge["to"])
            
            # Add conditional edges
            for cond_edge in template.conditional_edges:
                condition_func = self._create_condition_function(cond_edge["condition"])
                workflow.add_conditional_edges(
                    cond_edge["from"],
                    condition_func,
                    cond_edge["mapping"]
                )
            
            # Set entry point
            workflow.set_entry_point(template.entry_point)
            
            # Compile with memory
            memory = MemorySaver()
            compiled_graph = workflow.compile(checkpointer=memory)
            
            return compiled_graph
            
        except Exception as e:
            print(f"❌ Failed to compile template {template.template_id}: {e}")
            return None
    
    def _create_placeholder_node(self, node_name: str):
        """Create a placeholder node for missing methods."""
        async def placeholder_node(state: AgentState) -> AgentState:
            print(f"⚠️  Placeholder node executed: {node_name}")
            return {
                **state,
                "reasoning_history": state.get("reasoning_history", []) + 
                                   [f"Placeholder node: {node_name}"]
            }
        return placeholder_node
    
    def _create_condition_function(self, condition_key: str):
        """Create condition function for conditional edges."""
        def condition_func(state: AgentState):
            value = state.get(condition_key, "default")
            return value if isinstance(value, str) else "default"
        return condition_func
    
    async def _create_tool_chain_executor_node(self, tool_sequence: List[str]):
        """Create specialized node for tool chain execution."""
        
        async def tool_chain_executor_node(state: AgentState) -> AgentState:
            """Execute pre-configured tool sequence."""
            
            print(f"🔧 Executing tool chain: {' -> '.join(tool_sequence)}")
            
            # Execute tools in sequence
            current_state = state
            for tool_name in tool_sequence:
                # Find and execute tool
                for tool in self.agent.tools:
                    if hasattr(tool, 'name') and tool.name == tool_name:
                        try:
                            # Execute tool (simplified)
                            result = await tool.ainvoke({"query": state.get("input", "")})
                            current_state["tool_outputs"] = current_state.get("tool_outputs", []) + [result]
                        except Exception as e:
                            print(f"⚠️  Tool {tool_name} failed: {e}")
                        break
            
            return {
                **current_state,
                "reasoning_history": current_state.get("reasoning_history", []) + 
                                   [f"Tool chain executed: {' -> '.join(tool_sequence)}"]
            }
        
        # Add method to agent dynamically
        setattr(self.agent, "_tool_chain_executor_node", tool_chain_executor_node)
    
    def switch_to_template(self, template_id: str) -> bool:
        """Switch to a different graph template."""
        
        if template_id not in self.compiled_graphs:
            print(f"❌ Template {template_id} not compiled")
            return False
        
        # Store current graph for rollback
        self.rollback_graphs[self.active_graph_id] = self.agent.graph
        
        # Switch to new graph
        self.agent.graph = self.compiled_graphs[template_id]
        self.active_graph_id = template_id
        
        print(f"🔄 Switched to graph template: {template_id}")
        return True
    
    def rollback_to_previous_graph(self) -> bool:
        """Rollback to previous graph if optimization fails."""
        
        if not self.rollback_graphs:
            return False
        
        # Get most recent rollback graph
        previous_id = list(self.rollback_graphs.keys())[-1]
        previous_graph = self.rollback_graphs[previous_id]
        
        # Restore previous graph
        self.agent.graph = previous_graph
        self.active_graph_id = previous_id
        
        print(f"🔙 Rolled back to graph: {previous_id}")
        return True
    
    def get_recompilation_status(self) -> Dict[str, Any]:
        """Get current status of graph recompilation system."""
        
        return {
            "active_template": self.active_graph_id,
            "available_templates": list(self.compiled_graphs.keys()),
            "template_performance": self.template_manager.get_template_rankings(),
            "optimization_history": self.meta_reflector.get_optimization_summary(),
            "rollback_available": len(self.rollback_graphs) > 0
        }
